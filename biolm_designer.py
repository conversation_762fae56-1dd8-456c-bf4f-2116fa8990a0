"""
Main interface for the biolm protein design system.
"""
import logging
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import argparse
import json

from config import config
from biolm_client import client, Molecule, PredictionRequest
from data_manager import data_manager
from workflow import ProteinDesignWorkflow, WorkflowTemplates
from analysis import analyzer


class BiolmDesigner:
    """Main interface for protein design workflows."""
    
    def __init__(self, config_file: Optional[str] = None):
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('biolm_designer.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # Load configuration
        if config_file:
            config.config_file = config_file
            config.load()
        
        # Ensure directories exist
        config.ensure_directories()
        
        # Validate configuration
        try:
            config.validate()
        except ValueError as e:
            self.logger.error(f"Configuration error: {e}")
            sys.exit(1)
        
        self.logger.info("BiolmDesigner initialized successfully")
    
    def predict_single_complex(self, proteins: List[Dict[str, str]], 
                              experiment_name: str = "single_prediction",
                              params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Predict structure for a single protein complex."""
        workflow = ProteinDesignWorkflow(experiment_name, "Single complex prediction")
        
        try:
            result = workflow.run_single_prediction(proteins, params)
            workflow.finalize()
            
            self.logger.info(f"Single prediction completed: {result['run_id']}")
            return result
            
        except Exception as e:
            self.logger.error(f"Single prediction failed: {e}")
            raise
    
    def predict_batch(self, protein_sets: List[List[Dict[str, str]]], 
                     experiment_name: str = "batch_prediction",
                     params_list: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """Predict structures for multiple protein complexes."""
        workflow = ProteinDesignWorkflow(experiment_name, "Batch predictions")
        
        try:
            results = workflow.run_batch_predictions(protein_sets, params_list)
            workflow.finalize()
            
            self.logger.info(f"Batch prediction completed: {len(results)} predictions")
            return results
            
        except Exception as e:
            self.logger.error(f"Batch prediction failed: {e}")
            raise
    
    def optimize_parameters(self, proteins: List[Dict[str, str]], 
                          parameter_grid: Dict[str, List[Any]],
                          experiment_name: str = "parameter_optimization") -> List[Dict[str, Any]]:
        """Optimize prediction parameters."""
        workflow = ProteinDesignWorkflow(experiment_name, "Parameter optimization")
        
        try:
            results = workflow.run_parameter_sweep(proteins, parameter_grid)
            workflow.finalize()
            
            self.logger.info(f"Parameter optimization completed: {len(results)} combinations tested")
            return results
            
        except Exception as e:
            self.logger.error(f"Parameter optimization failed: {e}")
            raise
    
    def design_variants(self, base_protein: Dict[str, str], 
                       mutations: List[Dict[str, str]],
                       experiment_name: str = "variant_design") -> List[Dict[str, Any]]:
        """Design and predict protein variants."""
        workflow = ProteinDesignWorkflow(experiment_name, "Protein variant design")
        
        try:
            results = workflow.run_sequence_variants(base_protein, mutations)
            workflow.finalize()
            
            self.logger.info(f"Variant design completed: {len(results)} variants tested")
            return results
            
        except Exception as e:
            self.logger.error(f"Variant design failed: {e}")
            raise
    
    def analyze_experiment(self, experiment_id: str) -> Dict[str, Any]:
        """Analyze experiment results."""
        try:
            analysis = analyzer.analyze_experiment(experiment_id)
            self.logger.info(f"Analysis completed for experiment {experiment_id}")
            return analysis
            
        except Exception as e:
            self.logger.error(f"Analysis failed for experiment {experiment_id}: {e}")
            raise
    
    def compare_experiments(self, experiment_ids: List[str]) -> Dict[str, Any]:
        """Compare multiple experiments."""
        try:
            comparison = analyzer.compare_experiments(experiment_ids)
            self.logger.info(f"Comparison completed for {len(experiment_ids)} experiments")
            return comparison
            
        except Exception as e:
            self.logger.error(f"Experiment comparison failed: {e}")
            raise
    
    def list_experiments(self, tags: Optional[List[str]] = None, 
                        status: Optional[str] = None) -> List[Dict[str, Any]]:
        """List experiments with optional filtering."""
        experiments = data_manager.list_experiments(tags=tags, status=status)
        return [exp.to_dict() for exp in experiments]
    
    def export_experiment(self, experiment_id: str, export_path: str) -> None:
        """Export experiment data."""
        try:
            data_manager.export_experiment(experiment_id, export_path)
            self.logger.info(f"Experiment {experiment_id} exported to {export_path}")
            
        except Exception as e:
            self.logger.error(f"Export failed for experiment {experiment_id}: {e}")
            raise
    
    def run_antibody_design(self, antigen_sequence: str, 
                           antibody_sequences: List[str],
                           experiment_name: str = "antibody_design") -> Dict[str, Any]:
        """Run antibody design workflow."""
        workflow = WorkflowTemplates.antibody_design(antigen_sequence, antibody_sequences)
        workflow.experiment.name = experiment_name
        
        try:
            # The template already runs the prediction
            workflow.finalize()
            
            # Analyze results
            analysis = self.analyze_experiment(workflow.experiment.id)
            
            self.logger.info(f"Antibody design completed: {workflow.experiment.id}")
            return analysis
            
        except Exception as e:
            self.logger.error(f"Antibody design failed: {e}")
            raise
    
    def run_protein_optimization(self, base_sequence: str, 
                               optimization_positions: List[int],
                               experiment_name: str = "protein_optimization") -> Dict[str, Any]:
        """Run protein optimization workflow."""
        workflow = WorkflowTemplates.protein_optimization(base_sequence, optimization_positions)
        workflow.experiment.name = experiment_name
        
        try:
            # The template already runs the predictions
            workflow.finalize()
            
            # Analyze results
            analysis = self.analyze_experiment(workflow.experiment.id)
            
            self.logger.info(f"Protein optimization completed: {workflow.experiment.id}")
            return analysis
            
        except Exception as e:
            self.logger.error(f"Protein optimization failed: {e}")
            raise
    
    def get_experiment_summary(self, experiment_id: str) -> Dict[str, Any]:
        """Get a quick summary of an experiment."""
        experiment = data_manager.get_experiment(experiment_id)
        if not experiment:
            raise ValueError(f"Experiment {experiment_id} not found")
        
        # Get run statistics
        with data_manager.db_path.open() as conn:
            cursor = conn.execute("""
                SELECT status, COUNT(*) FROM experiment_runs 
                WHERE experiment_id = ? 
                GROUP BY status
            """, (experiment_id,))
            
            status_counts = dict(cursor.fetchall())
        
        return {
            "experiment": experiment.to_dict(),
            "run_counts": status_counts,
            "total_runs": sum(status_counts.values())
        }


def main():
    """Command-line interface."""
    parser = argparse.ArgumentParser(description="BiolM Protein Design System")
    parser.add_argument("--config", help="Configuration file path")
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Predict command
    predict_parser = subparsers.add_parser("predict", help="Run single prediction")
    predict_parser.add_argument("--proteins", required=True, help="JSON file with protein data")
    predict_parser.add_argument("--name", default="prediction", help="Experiment name")
    predict_parser.add_argument("--params", help="JSON file with parameters")
    
    # Batch command
    batch_parser = subparsers.add_parser("batch", help="Run batch predictions")
    batch_parser.add_argument("--protein-sets", required=True, help="JSON file with protein sets")
    batch_parser.add_argument("--name", default="batch", help="Experiment name")
    
    # Analyze command
    analyze_parser = subparsers.add_parser("analyze", help="Analyze experiment")
    analyze_parser.add_argument("--experiment-id", required=True, help="Experiment ID")
    
    # List command
    list_parser = subparsers.add_parser("list", help="List experiments")
    list_parser.add_argument("--tags", nargs="*", help="Filter by tags")
    list_parser.add_argument("--status", help="Filter by status")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Initialize designer
    designer = BiolmDesigner(args.config)
    
    try:
        if args.command == "predict":
            with open(args.proteins, 'r') as f:
                proteins = json.load(f)
            
            params = None
            if args.params:
                with open(args.params, 'r') as f:
                    params = json.load(f)
            
            result = designer.predict_single_complex(proteins, args.name, params)
            print(json.dumps(result, indent=2, default=str))
        
        elif args.command == "batch":
            with open(args.protein_sets, 'r') as f:
                protein_sets = json.load(f)
            
            results = designer.predict_batch(protein_sets, args.name)
            print(json.dumps(results, indent=2, default=str))
        
        elif args.command == "analyze":
            analysis = designer.analyze_experiment(args.experiment_id)
            print(json.dumps(analysis, indent=2, default=str))
        
        elif args.command == "list":
            experiments = designer.list_experiments(args.tags, args.status)
            print(json.dumps(experiments, indent=2, default=str))
    
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
