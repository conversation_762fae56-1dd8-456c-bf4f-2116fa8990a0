#!/usr/bin/env python3
"""
Simple FASTA-based interface for Chai-1 predictions via biolm API.
Clear, easy, and trackable protein structure predictions.
"""
import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import argparse
import json

from biolm_designer import <PERSON><PERSON><PERSON><PERSON>esigner
from config import config


class FastaPredictor:
    """Simple interface for FASTA-based Chai-1 predictions."""
    
    def __init__(self):
        self.designer = BiolmDesigner()
        print("🧬 FASTA Predictor for Chai-1 via BiolM API")
        print("=" * 50)
    
    def parse_fasta(self, fasta_file: str) -> List[Dict[str, str]]:
        """Parse FASTA file and return list of proteins."""
        proteins = []
        current_name = None
        current_sequence = []
        
        with open(fasta_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('>'):
                    # Save previous protein if exists
                    if current_name and current_sequence:
                        proteins.append({
                            "name": current_name,
                            "sequence": "".join(current_sequence)
                        })
                    
                    # Start new protein
                    current_name = line[1:].strip()  # Remove '>'
                    current_sequence = []
                elif line:
                    # Add to current sequence (remove any whitespace)
                    current_sequence.append(line.replace(" ", "").upper())
        
        # Don't forget the last protein
        if current_name and current_sequence:
            proteins.append({
                "name": current_name,
                "sequence": "".join(current_sequence)
            })
        
        return proteins
    
    def predict_from_fasta(self, fasta_file: str, experiment_name: Optional[str] = None,
                          params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Run Chai-1 prediction from FASTA file."""

        # Parse FASTA file
        print(f"📄 Reading FASTA file: {fasta_file}")
        proteins = self.parse_fasta(fasta_file)

        if not proteins:
            raise ValueError(f"No proteins found in FASTA file: {fasta_file}")

        print(f"✅ Found {len(proteins)} protein(s):")
        for i, protein in enumerate(proteins, 1):
            print(f"   {i}. {protein['name']} ({len(protein['sequence'])} residues)")

        # Generate experiment name if not provided
        if not experiment_name:
            fasta_name = Path(fasta_file).stem
            experiment_name = f"chai1_{fasta_name}"

        print(f"\n🚀 Running Chai-1 prediction...")
        print(f"   Experiment: {experiment_name}")
        if params:
            print(f"   Parameters: {params}")
        print(f"   Note: This may take 1-2 minutes to complete...")

        # Run prediction
        try:
            result = self.designer.predict_single_complex(
                proteins=proteins,
                experiment_name=experiment_name,
                params=params
            )
        except Exception as e:
            error_msg = str(e)
            if "spending limit" in error_msg.lower():
                print(f"\n❌ API Spending Limit Reached!")
                print(f"   Error: {error_msg}")
                print(f"   Solution: Visit https://biolm.ai to upgrade your plan")
                print(f"   Note: Your FASTA file was parsed correctly - this is a billing issue, not a technical one")
                raise
            elif "403" in error_msg:
                print(f"\n❌ API Access Denied!")
                print(f"   Error: {error_msg}")
                print(f"   Check your API token and account status at https://biolm.ai")
                raise
            else:
                print(f"\n❌ API Request Failed!")
                print(f"   Error: {error_msg}")
                raise

        # Print results
        print(f"\n✅ Prediction completed!")
        print(f"   Run ID: {result['run_id']}")
        print(f"   Experiment ID: {result.get('experiment_id', 'N/A')}")

        # Display quality metrics
        if 'confidence_scores' in result:
            conf = result['confidence_scores']
            if 'mean' in conf:
                print(f"   Mean pLDDT: {conf['mean']:.1f}")
                print(f"   pLDDT Range: {conf['min']:.1f} - {conf['max']:.1f}")

                if 'quality_breakdown' in conf:
                    breakdown = conf['quality_breakdown']
                    print(f"   Quality Distribution:")
                    print(f"     Very High (≥90): {breakdown['very_high_confidence_pct']:.1f}%")
                    print(f"     High (70-90): {breakdown['high_confidence_pct']:.1f}%")
                    print(f"     Medium (50-70): {breakdown['medium_confidence_pct']:.1f}%")
                    print(f"     Low (<50): {breakdown['low_confidence_pct']:.1f}%")
            elif 'average' in conf:
                print(f"   Confidence: {conf['average']:.3f}")

        # Save results summary
        self._save_results_summary(result, fasta_file, experiment_name)

        return result
    
    def predict_multiple_fastas(self, fasta_files: List[str], 
                               experiment_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """Run predictions for multiple FASTA files."""
        
        if not experiment_name:
            experiment_name = f"chai1_batch_{len(fasta_files)}_files"
        
        print(f"🔄 Running batch predictions for {len(fasta_files)} FASTA files")
        print(f"   Experiment: {experiment_name}")
        
        results = []
        for i, fasta_file in enumerate(fasta_files, 1):
            try:
                print(f"\n--- Processing file {i}/{len(fasta_files)}: {fasta_file} ---")
                
                # Use individual experiment names for each file
                file_exp_name = f"{experiment_name}_{Path(fasta_file).stem}"
                
                result = self.predict_from_fasta(
                    fasta_file=fasta_file,
                    experiment_name=file_exp_name
                )
                results.append(result)
                
            except Exception as e:
                print(f"❌ Failed to process {fasta_file}: {e}")
                results.append({"fasta_file": fasta_file, "error": str(e)})
        
        # Summary
        successful = [r for r in results if "error" not in r]
        failed = [r for r in results if "error" in r]
        
        print(f"\n📊 Batch Summary:")
        print(f"   Total files: {len(fasta_files)}")
        print(f"   Successful: {len(successful)}")
        print(f"   Failed: {len(failed)}")
        print(f"   Success rate: {len(successful)/len(fasta_files):.1%}")
        
        return results
    
    def _save_results_summary(self, result: Dict[str, Any], fasta_file: str,
                             experiment_name: str) -> None:
        """Save a comprehensive results summary with quality metrics."""
        confidence_scores = result.get("confidence_scores", {})

        # Create quality assessment
        quality_assessment = "Unknown"
        if 'mean' in confidence_scores:
            mean_plddt = confidence_scores['mean']
            if mean_plddt >= 90:
                quality_assessment = "Excellent"
            elif mean_plddt >= 80:
                quality_assessment = "Very Good"
            elif mean_plddt >= 70:
                quality_assessment = "Good"
            elif mean_plddt >= 60:
                quality_assessment = "Fair"
            else:
                quality_assessment = "Poor"

        summary = {
            "experiment_name": experiment_name,
            "fasta_file": fasta_file,
            "run_id": result.get("run_id"),
            "quality_assessment": quality_assessment,
            "confidence_scores": confidence_scores,
            "structure_info": {
                "cif_length": len(result.get("cif_data", "")),
                "has_structure": len(result.get("cif_data", "")) > 0
            },
            "timestamp": result.get("timestamp"),
            "proteins": result.get("proteins", [])
        }

        # Add quality insights
        if 'quality_breakdown' in confidence_scores:
            breakdown = confidence_scores['quality_breakdown']
            insights = []

            if breakdown['very_high_confidence_pct'] > 70:
                insights.append("Excellent overall confidence - structure is highly reliable")
            elif breakdown['very_high_confidence_pct'] + breakdown['high_confidence_pct'] > 80:
                insights.append("Good overall confidence - structure is reliable")
            elif breakdown['low_confidence_pct'] > 30:
                insights.append("Some regions have low confidence - interpret with caution")

            if breakdown['low_confidence_pct'] > 50:
                insights.append("High proportion of low-confidence regions - consider experimental validation")

            summary["quality_insights"] = insights

        # Save to results directory
        results_dir = Path("results")
        results_dir.mkdir(exist_ok=True)

        summary_file = results_dir / f"{experiment_name}_summary.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)

        print(f"   Summary saved: {summary_file}")
        print(f"   Overall Quality: {quality_assessment}")
    
    def list_recent_predictions(self, limit: int = 10) -> None:
        """List recent predictions."""
        experiments = self.designer.list_experiments()
        
        print(f"\n📋 Recent Predictions (last {limit}):")
        print("-" * 80)
        
        for i, exp in enumerate(experiments[:limit]):
            status_emoji = "✅" if exp["status"] == "completed" else "🔄" if exp["status"] == "running" else "❌"
            print(f"{status_emoji} {exp['name']}")
            print(f"   ID: {exp['id']}")
            print(f"   Created: {exp['created_at']}")
            print(f"   Status: {exp['status']}")
            
            # Get run summary
            try:
                summary = self.designer.get_experiment_summary(exp['id'])
                run_counts = summary.get('run_counts', {})
                total_runs = summary.get('total_runs', 0)
                print(f"   Runs: {total_runs} total")
                if run_counts:
                    status_str = ", ".join([f"{k}: {v}" for k, v in run_counts.items()])
                    print(f"   Status breakdown: {status_str}")
            except:
                pass
            
            print()
    
    def analyze_prediction(self, experiment_id: str) -> None:
        """Analyze a specific prediction."""
        print(f"🔍 Analyzing prediction: {experiment_id}")
        
        try:
            analysis = self.designer.analyze_experiment(experiment_id)
            
            print(f"\n📊 Analysis Results:")
            print(f"   Experiment: {analysis['experiment']['name']}")
            print(f"   Description: {analysis['experiment']['description']}")
            
            summary = analysis['summary']
            print(f"   Total runs: {summary['total_runs']}")
            print(f"   Successful: {summary['successful_runs']}")
            print(f"   Success rate: {summary['success_rate']:.1%}")
            
            if 'confidence_stats' in summary:
                conf = summary['confidence_stats']
                print(f"   Avg confidence: {conf['mean']:.3f} ± {conf['std']:.3f}")
                print(f"   Confidence range: {conf['min']:.3f} - {conf['max']:.3f}")
            
            print(f"\n💡 Insights:")
            for insight in analysis['insights']:
                print(f"   • {insight}")
                
        except Exception as e:
            print(f"❌ Analysis failed: {e}")


def main():
    """Command-line interface for FASTA predictions."""
    parser = argparse.ArgumentParser(
        description="Run Chai-1 predictions from FASTA files via BiolM API",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Single FASTA file
  python fasta_predictor.py predict my_protein.fasta
  
  # Multiple FASTA files
  python fasta_predictor.py predict protein1.fasta protein2.fasta
  
  # With custom experiment name
  python fasta_predictor.py predict my_protein.fasta --name "my_experiment"
  
  # With custom parameters
  python fasta_predictor.py predict my_protein.fasta --params '{"num_trunk_recycles": 6}'
  
  # List recent predictions
  python fasta_predictor.py list
  
  # Analyze specific prediction
  python fasta_predictor.py analyze exp_20241209_143022_my_experiment
        """
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Predict command
    predict_parser = subparsers.add_parser("predict", help="Run prediction from FASTA file(s)")
    predict_parser.add_argument("fasta_files", nargs="+", help="FASTA file(s) to predict")
    predict_parser.add_argument("--name", help="Experiment name (auto-generated if not provided)")
    predict_parser.add_argument("--params", help="JSON string with prediction parameters")
    
    # List command
    list_parser = subparsers.add_parser("list", help="List recent predictions")
    list_parser.add_argument("--limit", type=int, default=10, help="Number of predictions to show")
    
    # Analyze command
    analyze_parser = subparsers.add_parser("analyze", help="Analyze prediction results")
    analyze_parser.add_argument("experiment_id", help="Experiment ID to analyze")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Set up API token (you can also set this in biolm_config.json)
    if not config.api.token:
        config.api.token = "6bf627a20fc4d573c6db14e5b1e52d673565a877a5bd37e421247c1715c39275"
    
    predictor = FastaPredictor()
    
    try:
        if args.command == "predict":
            # Parse parameters if provided
            params = None
            if args.params:
                params = json.loads(args.params)
            
            # Check if files exist
            for fasta_file in args.fasta_files:
                if not Path(fasta_file).exists():
                    print(f"❌ FASTA file not found: {fasta_file}")
                    return
            
            # Run predictions
            if len(args.fasta_files) == 1:
                predictor.predict_from_fasta(
                    fasta_file=args.fasta_files[0],
                    experiment_name=args.name,
                    params=params
                )
            else:
                predictor.predict_multiple_fastas(
                    fasta_files=args.fasta_files,
                    experiment_name=args.name
                )
        
        elif args.command == "list":
            predictor.list_recent_predictions(args.limit)
        
        elif args.command == "analyze":
            predictor.analyze_prediction(args.experiment_id)
    
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
