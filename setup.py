#!/usr/bin/env python3
"""
Setup script for the biolm protein design system.
"""
import os
import json
from pathlib import Path
from config import config


def setup_environment():
    """Set up the environment for the biolm system."""
    print("Setting up BiolM Protein Design System...")
    
    # Create configuration file
    config_data = {
        "api": {
            "base_url": "https://biolm.ai/api/v3",
            "token": "",  # User needs to set this
            "timeout": 300,
            "max_retries": 3,
            "retry_delay": 1.0
        },
        "chai": {
            "num_trunk_recycles": 4,
            "num_diffusion_timesteps": 180,
            "num_diffn_samples": 1,
            "use_esm_embeddings": True,
            "seed": 42,
            "include": []
        },
        "storage": {
            "base_dir": "experiments",
            "results_dir": "results",
            "logs_dir": "logs",
            "cache_dir": "cache",
            "backup_dir": "backups"
        },
        "experiment": {
            "name": "",
            "description": "",
            "tags": [],
            "metadata": {}
        }
    }
    
    config_file = "biolm_config.json"
    if not Path(config_file).exists():
        with open(config_file, 'w') as f:
            json.dump(config_data, f, indent=2)
        print(f"Created configuration file: {config_file}")
    else:
        print(f"Configuration file already exists: {config_file}")
    
    # Create directory structure
    base_dir = Path("experiments")
    directories = [
        base_dir,
        base_dir / "results",
        base_dir / "logs", 
        base_dir / "cache",
        base_dir / "backups"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        print(f"Created directory: {directory}")
    
    # Create example data files
    create_example_data()
    
    print("\nSetup completed!")
    print("\nNext steps:")
    print("1. Edit biolm_config.json and add your API token")
    print("2. Install dependencies: pip install -r requirements.txt")
    print("3. Run examples: python examples.py")
    print("4. Or use the command line: python biolm_designer.py --help")


def create_example_data():
    """Create example data files for testing."""
    
    # Example proteins for single prediction
    single_proteins = [
        {
            "name": "protein_A",
            "sequence": "EVQLLESGGGLVQPGGSLRLSCAASGLDISSVAMSWVRQAPGKGLEWVSAISPDGSYTYYADSVKGRFTISRDNSKNTLYLQMNSLRAEDTAVYYCAKGGGTLSKRTVWGQGTLVTVSS"
        },
        {
            "name": "protein_B", 
            "sequence": "LYWDDLKRKLSEKLDSTDFTSTIKLLNENSYVPREAGSQKDENLALYVENEFREFKLSKVWRDQHFVKIQVKDSAQNSVIIVDKNGRLVYLVENPGGYVAYSKAATVTGKLVHANFGTKKDFEDLYTPVNGSIVIVRAGKITFAEKVANAESLNAIGVLIYMDQTKFPIVNAELSFFGHAHLGTGDPYTPGFPSFNHTQFPPSRSSGLPNIPVQTISRAAAEKLFGNMEGDCPSDWKTDSTCRMVTSESKNVKLTVSNVLKEIKILNIFGVIKGFVEPDHYVVVGAQRDAWGPGAAKSGVGTALLLKLAQMFSDMVLKDGFQPSRSIIFASWSAGDFGSVGATEWLEGYLSSLHLKAFTYINLDKAVLGTSNFKVSASPLLYTLIEKTMQNVKHPVTGQFLYQDSNWASKVEKLTLDNAAFPFLAYSGIPAVSFCFCEDTDYPYLGTTMDTYKELIERIPELNKVARAAAEVAGQFVIKLTHDVELNLDYEEYNSQLLSFVRDLNQYRADIKEMGLSLQWLYSARGDFFRATSRLTTDFGNAEKTDRFVMKKLNDRVMRVEYHFLSPYVSPKESPFRHVFWGSGSHTLPALLENLKLRKQNNGAFNETLFRNQLALATWTIQGAANALSGDVWDIDNEF"
        }
    ]
    
    with open("example_proteins.json", 'w') as f:
        json.dump(single_proteins, f, indent=2)
    print("Created example_proteins.json")
    
    # Example protein sets for batch prediction
    protein_sets = [
        [
            {"name": "set1_A", "sequence": "MKLLNVINFVFLMFVSSSKILGSCQKDVNKDWRQFQKQEQGIDLYKHMFENYPPLRKYFKSREEYTAEDVQNDPFFAKQGQKILLACHVLCATYDDRETFNAYTRELLDRHARDHVHMPPEVWTDFWKLFEEYLGKKTTLDEPTKQAWHEIGREFAKEINKHGRVLVVNNAGIGILDDHHFNKFFKRQHINYLWNEQDMIAETEEHMHKTPGPGILSMANAGPNTNGSQFFICTAKTEWLDGKHVVFGKVKEGMNIVEAMERFGSRNGKTSKKITIADCGQLE"},
            {"name": "set1_B", "sequence": "MKKLLFTCLLLVVSSGSAFSSCQKDVNKDWRQFQKQEQGIDLYKHMFENYPPLRKYFKSREEYTAEDVQNDPFFAKQGQKILLACHVLCATYDDRETFNAYTRELLDRHARDHVHMPPEVWTDFWKLFEEYLGKKTTLDEPTKQAWHEIGREFAKEINKHGRVLVVNNAGIGILDDHHFNKFFKRQHINYLWNEQDMIAETEEHMHKTPGPGILSMANAGPNTNGSQFFICTAKTEWLDGKHVVFGKVKEGMNIVEAMERFGSRNGKTSKKITIADCGQLE"}
        ],
        [
            {"name": "set2_A", "sequence": "EVQLVESGGGLVQPGGSLRLSCAASGFTFSSYAMSWVRQAPGKGLEWVSAISGSGGSTYYADSVKGRFTISRDNSKNTLYLQMNSLRAEDTAVYYCAKVSYLSTASSLDYWGQGTLVTVSS"},
            {"name": "set2_B", "sequence": "DIQMTQSPSSLSASVGDRVTITCRASQDVNTAVAWYQQKPGKAPKLLIYSASFLYSGVPSRFSGSRSGTDFTLTISSLQPEDFATYYCQQHYTTPPTFGQGTKVEIKRTVAAPSVFIFPPSDEQLKSGTASVVCLLNNFYPREAKVQWKVDNALQSGNSQESVTEQDSKDSTYSLSSTLTLSKADYEKHKVYACEVTHQGLSSPVTKSFNRGEC"}
        ]
    ]
    
    with open("example_protein_sets.json", 'w') as f:
        json.dump(protein_sets, f, indent=2)
    print("Created example_protein_sets.json")
    
    # Example parameters
    example_params = {
        "num_trunk_recycles": 6,
        "num_diffusion_timesteps": 250,
        "use_esm_embeddings": True,
        "seed": 123
    }
    
    with open("example_params.json", 'w') as f:
        json.dump(example_params, f, indent=2)
    print("Created example_params.json")


if __name__ == "__main__":
    setup_environment()
