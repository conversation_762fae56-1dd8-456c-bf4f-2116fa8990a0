"""
Data management system for organizing experiments, results, and metadata.
"""
import json
import shutil
import sqlite3
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

from config import config
from biolm_client import PredictionResult, PredictionRequest


@dataclass
class Experiment:
    """Represents an experiment."""
    id: str
    name: str
    description: str
    created_at: datetime
    tags: List[str]
    metadata: Dict[str, Any]
    status: str = "created"  # created, running, completed, failed
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Experiment':
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        return cls(**data)


@dataclass
class ExperimentRun:
    """Represents a single run within an experiment."""
    id: str
    experiment_id: str
    request_hash: str
    created_at: datetime
    status: str = "pending"  # pending, running, completed, failed
    result_path: Optional[str] = None
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExperimentRun':
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        return cls(**data)


class DataManager:
    """Manages experiment data, results, and metadata."""
    
    def __init__(self, base_dir: Optional[str] = None):
        self.base_dir = Path(base_dir or config.storage.base_dir)
        self.db_path = self.base_dir / "experiments.db"
        
        # Ensure directories exist
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Initialize database
        self._init_database()
    
    def _init_database(self) -> None:
        """Initialize SQLite database for metadata."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS experiments (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    created_at TEXT NOT NULL,
                    tags TEXT,  -- JSON array
                    metadata TEXT,  -- JSON object
                    status TEXT DEFAULT 'created'
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS experiment_runs (
                    id TEXT PRIMARY KEY,
                    experiment_id TEXT NOT NULL,
                    request_hash TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    status TEXT DEFAULT 'pending',
                    result_path TEXT,
                    error_message TEXT,
                    metadata TEXT,  -- JSON object
                    FOREIGN KEY (experiment_id) REFERENCES experiments (id)
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_experiment_runs_experiment_id 
                ON experiment_runs (experiment_id)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_experiment_runs_request_hash 
                ON experiment_runs (request_hash)
            """)
    
    def create_experiment(self, name: str, description: str = "", 
                         tags: List[str] = None, metadata: Dict[str, Any] = None) -> Experiment:
        """Create a new experiment."""
        experiment_id = f"exp_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{name.replace(' ', '_')}"
        
        experiment = Experiment(
            id=experiment_id,
            name=name,
            description=description,
            created_at=datetime.now(),
            tags=tags or [],
            metadata=metadata or {}
        )
        
        # Create experiment directory
        exp_dir = self.get_experiment_dir(experiment_id)
        exp_dir.mkdir(parents=True, exist_ok=True)
        
        # Save to database
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO experiments (id, name, description, created_at, tags, metadata, status)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                experiment.id,
                experiment.name,
                experiment.description,
                experiment.created_at.isoformat(),
                json.dumps(experiment.tags),
                json.dumps(experiment.metadata),
                experiment.status
            ))
        
        # Save experiment metadata to file
        exp_file = exp_dir / "experiment.json"
        with open(exp_file, 'w') as f:
            json.dump(experiment.to_dict(), f, indent=2)
        
        self.logger.info(f"Created experiment: {experiment_id}")
        return experiment
    
    def get_experiment(self, experiment_id: str) -> Optional[Experiment]:
        """Get experiment by ID."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT id, name, description, created_at, tags, metadata, status
                FROM experiments WHERE id = ?
            """, (experiment_id,))
            
            row = cursor.fetchone()
            if row:
                return Experiment(
                    id=row[0],
                    name=row[1],
                    description=row[2],
                    created_at=datetime.fromisoformat(row[3]),
                    tags=json.loads(row[4]),
                    metadata=json.loads(row[5]),
                    status=row[6]
                )
        return None
    
    def list_experiments(self, tags: List[str] = None, status: str = None) -> List[Experiment]:
        """List experiments with optional filtering."""
        query = "SELECT id, name, description, created_at, tags, metadata, status FROM experiments"
        params = []
        conditions = []
        
        if status:
            conditions.append("status = ?")
            params.append(status)
        
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
        
        query += " ORDER BY created_at DESC"
        
        experiments = []
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(query, params)
            for row in cursor.fetchall():
                exp = Experiment(
                    id=row[0],
                    name=row[1],
                    description=row[2],
                    created_at=datetime.fromisoformat(row[3]),
                    tags=json.loads(row[4]),
                    metadata=json.loads(row[5]),
                    status=row[6]
                )
                
                # Filter by tags if specified
                if tags and not any(tag in exp.tags for tag in tags):
                    continue
                
                experiments.append(exp)
        
        return experiments
    
    def update_experiment_status(self, experiment_id: str, status: str) -> None:
        """Update experiment status."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                UPDATE experiments SET status = ? WHERE id = ?
            """, (status, experiment_id))
        
        self.logger.info(f"Updated experiment {experiment_id} status to {status}")
    
    def add_experiment_run(self, experiment_id: str, request: PredictionRequest) -> ExperimentRun:
        """Add a new run to an experiment."""
        run_id = f"run_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        run = ExperimentRun(
            id=run_id,
            experiment_id=experiment_id,
            request_hash=request.get_hash(),
            created_at=datetime.now(),
            metadata={
                "molecules": [mol.name for mol in request.molecules],
                "params": request.params
            }
        )
        
        # Save to database
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO experiment_runs 
                (id, experiment_id, request_hash, created_at, status, metadata)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                run.id,
                run.experiment_id,
                run.request_hash,
                run.created_at.isoformat(),
                run.status,
                json.dumps(run.metadata)
            ))
        
        return run
    
    def update_run_result(self, run_id: str, result: PredictionResult, 
                         status: str = "completed") -> None:
        """Update run with result."""
        # Save result to file
        exp_dir = self.get_experiment_dir(self._get_experiment_id_for_run(run_id))
        result_dir = exp_dir / "results"
        result_dir.mkdir(exist_ok=True)
        
        # Save raw result
        result_file = result_dir / f"{run_id}_result.json"
        with open(result_file, 'w') as f:
            json.dump(result.raw_response, f, indent=2)
        
        # Save CIF if available
        try:
            cif_file = result_dir / f"{run_id}_structure.cif"
            result.save_cif(cif_file)
        except Exception as e:
            self.logger.warning(f"Could not save CIF for run {run_id}: {e}")
        
        # Update database
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                UPDATE experiment_runs 
                SET status = ?, result_path = ?
                WHERE id = ?
            """, (status, str(result_file), run_id))
    
    def get_experiment_dir(self, experiment_id: str) -> Path:
        """Get experiment directory path."""
        return self.base_dir / experiment_id
    
    def _get_experiment_id_for_run(self, run_id: str) -> str:
        """Get experiment ID for a run."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT experiment_id FROM experiment_runs WHERE id = ?
            """, (run_id,))
            row = cursor.fetchone()
            if row:
                return row[0]
        raise ValueError(f"Run {run_id} not found")
    
    def export_experiment(self, experiment_id: str, export_path: str) -> None:
        """Export experiment data to a directory."""
        export_dir = Path(export_path)
        export_dir.mkdir(parents=True, exist_ok=True)
        
        # Copy experiment directory
        exp_dir = self.get_experiment_dir(experiment_id)
        if exp_dir.exists():
            shutil.copytree(exp_dir, export_dir / experiment_id, dirs_exist_ok=True)
        
        self.logger.info(f"Exported experiment {experiment_id} to {export_path}")


# Global data manager instance
data_manager = DataManager()
