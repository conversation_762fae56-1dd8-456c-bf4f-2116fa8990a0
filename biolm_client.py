"""
Robust client for interacting with biolm API.
"""
import time
import json
import logging
import requests
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib

from config import config


@dataclass
class Molecule:
    """Represents a molecule for prediction."""
    name: str
    type: str  # 'protein', 'dna', 'rna', 'ligand'
    sequence: str
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class PredictionRequest:
    """Represents a prediction request."""
    molecules: List[Molecule]
    params: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.params is None:
            self.params = asdict(config.chai)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "params": self.params,
            "items": [{"molecules": [mol.to_dict() for mol in self.molecules]}]
        }
    
    def get_hash(self) -> str:
        """Generate a hash for caching purposes."""
        content = json.dumps(self.to_dict(), sort_keys=True)
        return hashlib.md5(content.encode()).hexdigest()


@dataclass
class PredictionResult:
    """Represents a prediction result."""
    request_hash: str
    raw_response: Dict[str, Any]
    cif_data: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def extract_cif(self) -> str:
        """Extract CIF data from raw response."""
        if self.cif_data:
            return self.cif_data
            
        try:
            self.cif_data = self.raw_response["results"][0][0]["cif"]
            return self.cif_data
        except (KeyError, IndexError) as e:
            raise ValueError(f"Could not extract CIF data: {e}")
    
    def save_cif(self, filepath: Union[str, Path]) -> None:
        """Save CIF data to file."""
        cif_data = self.extract_cif()
        Path(filepath).write_text(cif_data)
    
    def get_confidence_scores(self) -> Dict[str, float]:
        """Extract confidence scores if available."""
        # This would need to be adapted based on actual API response structure
        scores = {}
        try:
            # Example extraction - adapt based on actual response format
            if "confidence" in self.raw_response:
                scores = self.raw_response["confidence"]
        except Exception as e:
            logging.warning(f"Could not extract confidence scores: {e}")
        return scores


class BiolmClient:
    """Client for interacting with biolm API."""
    
    def __init__(self, api_config=None):
        self.config = api_config or config.api
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Token {self.config.token}",
            "Content-Type": "application/json"
        })
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Cache for results
        self.cache_dir = Path(config.storage.cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
    
    def _make_request(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Make a request to the API with retry logic."""
        url = f"{self.config.base_url}/{endpoint}"
        
        for attempt in range(self.config.max_retries):
            try:
                self.logger.info(f"Making request to {url} (attempt {attempt + 1})")
                
                response = self.session.post(
                    url, 
                    json=data, 
                    timeout=self.config.timeout
                )
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:  # Rate limit
                    wait_time = self.config.retry_delay * (2 ** attempt)
                    self.logger.warning(f"Rate limited, waiting {wait_time}s")
                    time.sleep(wait_time)
                    continue
                else:
                    response.raise_for_status()
                    
            except requests.exceptions.RequestException as e:
                self.logger.error(f"Request failed (attempt {attempt + 1}): {e}")
                if attempt == self.config.max_retries - 1:
                    raise
                time.sleep(self.config.retry_delay * (2 ** attempt))
        
        raise Exception(f"Failed to complete request after {self.config.max_retries} attempts")
    
    def _get_cache_path(self, request_hash: str) -> Path:
        """Get cache file path for a request."""
        return self.cache_dir / f"{request_hash}.json"
    
    def _load_from_cache(self, request_hash: str) -> Optional[PredictionResult]:
        """Load result from cache if available."""
        cache_path = self._get_cache_path(request_hash)
        if cache_path.exists():
            try:
                with open(cache_path, 'r') as f:
                    data = json.load(f)
                return PredictionResult(**data)
            except Exception as e:
                self.logger.warning(f"Failed to load from cache: {e}")
        return None
    
    def _save_to_cache(self, result: PredictionResult) -> None:
        """Save result to cache."""
        cache_path = self._get_cache_path(result.request_hash)
        try:
            with open(cache_path, 'w') as f:
                json.dump(asdict(result), f, indent=2)
        except Exception as e:
            self.logger.warning(f"Failed to save to cache: {e}")
    
    def predict_chai1(self, request: PredictionRequest, use_cache: bool = True) -> PredictionResult:
        """Make a Chai-1 prediction."""
        request_hash = request.get_hash()
        
        # Check cache first
        if use_cache:
            cached_result = self._load_from_cache(request_hash)
            if cached_result:
                self.logger.info(f"Using cached result for {request_hash}")
                return cached_result
        
        # Make the request
        self.logger.info(f"Making Chai-1 prediction for {request_hash}")
        raw_response = self._make_request("chai1/predict/", request.to_dict())
        
        # Create result
        result = PredictionResult(
            request_hash=request_hash,
            raw_response=raw_response,
            metadata={
                "timestamp": time.time(),
                "molecules": [mol.name for mol in request.molecules]
            }
        )
        
        # Cache the result
        if use_cache:
            self._save_to_cache(result)
        
        return result
    
    def predict_protein_complex(self, proteins: List[Dict[str, str]], **kwargs) -> PredictionResult:
        """Convenience method for predicting protein complexes."""
        molecules = [
            Molecule(name=p["name"], type="protein", sequence=p["sequence"])
            for p in proteins
        ]
        
        request = PredictionRequest(molecules=molecules)
        if kwargs:
            request.params.update(kwargs)
        
        return self.predict_chai1(request)
    
    def batch_predict(self, requests: List[PredictionRequest], 
                     delay_between_requests: float = 1.0) -> List[PredictionResult]:
        """Make multiple predictions with rate limiting."""
        results = []
        
        for i, request in enumerate(requests):
            self.logger.info(f"Processing request {i + 1}/{len(requests)}")
            
            try:
                result = self.predict_chai1(request)
                results.append(result)
                
                # Rate limiting
                if i < len(requests) - 1:
                    time.sleep(delay_between_requests)
                    
            except Exception as e:
                self.logger.error(f"Failed to process request {i + 1}: {e}")
                # Continue with other requests
                continue
        
        return results


# Global client instance
client = BiolmClient()
