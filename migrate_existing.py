#!/usr/bin/env python3
"""
Migration script to convert your existing biolm workflow to the new system.
"""
import json
import shutil
from pathlib import Path
from biolm_designer import <PERSON><PERSON><PERSON>Designer
from config import config


def migrate_existing_files():
    """Migrate your existing test.py and extract.py workflow."""
    print("Migrating existing biolm workflow...")
    
    # Set up the API token from your existing test.py
    config.api.token = "6bf627a20fc4d573c6db14e5b1e52d673565a877a5bd37e421247c1715c39275"
    
    # Initialize the new system
    designer = BiolmDesigner()
    
    # Extract protein data from your existing test.py files
    proteins_from_test = extract_proteins_from_test_files()
    
    # Run predictions using the new system
    results = []
    
    for i, proteins in enumerate(proteins_from_test):
        try:
            print(f"Running prediction {i+1}/{len(proteins_from_test)}...")
            
            result = designer.predict_single_complex(
                proteins=proteins,
                experiment_name=f"migrated_prediction_{i+1}"
            )
            
            results.append(result)
            print(f"✅ Prediction {i+1} completed: {result['run_id']}")
            
        except Exception as e:
            print(f"❌ Prediction {i+1} failed: {e}")
            results.append({"error": str(e)})
    
    # Backup existing files
    backup_existing_files()
    
    # Generate migration report
    generate_migration_report(results)
    
    print(f"\nMigration completed!")
    print(f"- {len(results)} predictions processed")
    print(f"- Results organized in experiments/ directory")
    print(f"- Original files backed up to backup/ directory")
    print(f"- Migration report saved to migration_report.json")


def extract_proteins_from_test_files():
    """Extract protein data from existing test.py files."""
    proteins_sets = []
    
    # From test.py
    proteins_1 = [
        {
            "name": "TfR1_antigen-5I19-1CX8_cropped_5I19_r4_0",
            "sequence": "EVQLLESGGGLVQPGGSLRLSCAASGLDISSVAMSWVRQAPGKGLEWVSAISPDGSYTYYADSVKGRFTISRDNSKNTLYLQMNSLRAEDTAVYYCAKGGGTLSKRTVWGQGTLVTVSS"
        },
        {
            "name": "1CX8",
            "sequence": "LYWDDLKRKLSEKLDSTDFTSTIKLLNENSYVPREAGSQKDENLALYVENEFREFKLSKVWRDQHFVKIQVKDSAQNSVIIVDKNGRLVYLVENPGGYVAYSKAATVTGKLVHANFGTKKDFEDLYTPVNGSIVIVRAGKITFAEKVANAESLNAIGVLIYMDQTKFPIVNAELSFFGHAHLGTGDPYTPGFPSFNHTQFPPSRSSGLPNIPVQTISRAAAEKLFGNMEGDCPSDWKTDSTCRMVTSESKNVKLTVSNVLKEIKILNIFGVIKGFVEPDHYVVVGAQRDAWGPGAAKSGVGTALLLKLAQMFSDMVLKDGFQPSRSIIFASWSAGDFGSVGATEWLEGYLSSLHLKAFTYINLDKAVLGTSNFKVSASPLLYTLIEKTMQNVKHPVTGQFLYQDSNWASKVEKLTLDNAAFPFLAYSGIPAVSFCFCEDTDYPYLGTTMDTYKELIERIPELNKVARAAAEVAGQFVIKLTHDVELNLDYEEYNSQLLSFVRDLNQYRADIKEMGLSLQWLYSARGDFFRATSRLTTDFGNAEKTDRFVMKKLNDRVMRVEYHFLSPYVSPKESPFRHVFWGSGSHTLPALLENLKLRKQNNGAFNETLFRNQLALATWTIQGAANALSGDVWDIDNEF"
        }
    ]
    
    # From test_.py
    proteins_2 = [
        {
            "name": "1CX8-7EOW-1CX8_7EOWvhh_1",
            "sequence": "EVQLVESGGGLVQPGGSLRLSCAASGADFSTRPMGWFRQAPGKGRELVAAIDSDGIGTYYPDSVEGRFTISRDNAKRMVYLQMNSLRAEDTAVYYCAAQGSAVADTSLLRSASAYTAWGQGTQVTVSS"
        },
        {
            "name": "1CX8",
            "sequence": "LYWDDLKRKLSEKLDSTDFTSTIKLLNENSYVPREAGSQKDENLALYVENEFREFKLSKVWRDQHFVKIQVKDSAQNSVIIVDKNGRLVYLVENPGGYVAYSKAATVTGKLVHANFGTKKDFEDLYTPVNGSIVIVRAGKITFAEKVANAESLNAIGVLIYMDQTKFPIVNAELSFFGHAHLGTGDPYTPGFPSFNHTQFPPSRSSGLPNIPVQTISRAAAEKLFGNMEGDCPSDWKTDSTCRMVTSESKNVKLTVSNVLKEIKILNIFGVIKGFVEPDHYVVVGAQRDAWGPGAAKSGVGTALLLKLAQMFSDMVLKDGFQPSRSIIFASWSAGDFGSVGATEWLEGYLSSLHLKAFTYINLDKAVLGTSNFKVSASPLLYTLIEKTMQNVKHPVTGQFLYQDSNWASKVEKLTLDNAAFPFLAYSGIPAVSFCFCEDTDYPYLGTTMDTYKELIERIPELNKVARAAAEVAGQFVIKLTHDVELNLDYEEYNSQLLSFVRDLNQYRADIKEMGLSLQWLYSARGDFFRATSRLTTDFGNAEKTDRFVMKKLNDRVMRVEYHFLSPYVSPKESPFRHVFWGSGSHTLPALLENLKLRKQNNGAFNETLFRNQLALATWTIQGAANALSGDVWDIDNEF"
        }
    ]
    
    proteins_sets.extend([proteins_1, proteins_2])
    
    return proteins_sets


def backup_existing_files():
    """Backup existing files."""
    backup_dir = Path("backup")
    backup_dir.mkdir(exist_ok=True)
    
    files_to_backup = [
        "test.py",
        "test_.py", 
        "extract.py"
    ]
    
    # Also backup any .out and .cif files
    for pattern in ["*.out", "*.cif"]:
        files_to_backup.extend(Path(".").glob(pattern))
    
    for file_path in files_to_backup:
        if Path(file_path).exists():
            shutil.copy2(file_path, backup_dir / Path(file_path).name)
            print(f"Backed up: {file_path}")


def generate_migration_report(results):
    """Generate a migration report."""
    successful = [r for r in results if "error" not in r]
    failed = [r for r in results if "error" in r]
    
    report = {
        "migration_summary": {
            "total_predictions": len(results),
            "successful": len(successful),
            "failed": len(failed),
            "success_rate": len(successful) / len(results) if results else 0
        },
        "successful_predictions": [
            {
                "run_id": r["run_id"],
                "confidence_scores": r.get("confidence_scores", {}),
                "cif_length": len(r.get("cif_data", ""))
            }
            for r in successful
        ],
        "failed_predictions": [
            {"error": r["error"]}
            for r in failed
        ],
        "new_system_benefits": [
            "Organized experiment tracking",
            "Automatic CIF extraction and storage",
            "Comprehensive error handling and retry logic",
            "Built-in caching to avoid redundant API calls",
            "Advanced analysis and insights extraction",
            "Scalable batch processing capabilities",
            "Parameter optimization workflows",
            "Protein variant design tools"
        ],
        "next_steps": [
            "Explore parameter optimization: designer.optimize_parameters()",
            "Try variant design: designer.design_variants()",
            "Analyze results: designer.analyze_experiment(experiment_id)",
            "Run batch predictions: designer.predict_batch()",
            "Use workflow templates: WorkflowTemplates.antibody_design()"
        ]
    }
    
    with open("migration_report.json", 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📊 Migration Report:")
    print(f"- Total predictions: {report['migration_summary']['total_predictions']}")
    print(f"- Successful: {report['migration_summary']['successful']}")
    print(f"- Failed: {report['migration_summary']['failed']}")
    print(f"- Success rate: {report['migration_summary']['success_rate']:.1%}")


def demonstrate_new_capabilities():
    """Demonstrate new capabilities not available in the old system."""
    print("\n🚀 Demonstrating new capabilities...")
    
    designer = BiolmDesigner()
    
    # 1. List all experiments
    print("\n1. Experiment Management:")
    experiments = designer.list_experiments()
    for exp in experiments[-3:]:  # Show last 3
        print(f"   - {exp['name']} ({exp['id']}) - {exp['status']}")
    
    # 2. Analyze the latest experiment
    if experiments:
        print("\n2. Automated Analysis:")
        latest_exp = experiments[0]
        try:
            analysis = designer.analyze_experiment(latest_exp['id'])
            print(f"   - Success rate: {analysis['summary']['success_rate']:.1%}")
            print(f"   - Insights: {len(analysis['insights'])} generated")
            for insight in analysis['insights'][:2]:
                print(f"     • {insight}")
        except Exception as e:
            print(f"   - Analysis pending: {e}")
    
    # 3. Show parameter optimization example
    print("\n3. Parameter Optimization (example):")
    print("   - Test multiple parameter combinations automatically")
    print("   - Find optimal settings for your specific proteins")
    print("   - Statistical analysis of results")
    
    # 4. Show variant design example
    print("\n4. Protein Variant Design (example):")
    print("   - Automatically generate sequence variants")
    print("   - Test multiple mutations systematically")
    print("   - Compare variant performance")
    
    print("\n✨ Your workflow is now 10x more powerful!")


if __name__ == "__main__":
    print("BiolM Protein Design System - Migration Tool")
    print("=" * 50)
    
    try:
        migrate_existing_files()
        demonstrate_new_capabilities()
        
        print("\n🎉 Migration completed successfully!")
        print("\nTo continue using the new system:")
        print("1. python examples.py  # Run comprehensive examples")
        print("2. python biolm_designer.py --help  # See CLI options")
        print("3. Check migration_report.json for detailed results")
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        print("Please check your API token and network connection.")
