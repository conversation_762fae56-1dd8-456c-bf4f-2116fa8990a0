#!/usr/bin/env python3
"""
Test script to verify quality metrics extraction from existing results.
"""
from pathlib import Path
from biolm_client import PredictionResult
import json

def test_quality_extraction():
    """Test extracting quality metrics from existing result."""
    
    # Load existing result
    result_file = Path("experiments/exp_20250714_102250_chai1_6WRV-5I19-6WRV_0_TFR1/results/run_20250714_102250_232869_result.json")
    
    if not result_file.exists():
        print("❌ Result file not found")
        return
    
    print("🔍 Testing Quality Metrics Extraction")
    print("=" * 50)
    
    # Load the result
    with open(result_file, 'r') as f:
        raw_response = json.load(f)
    
    # Create PredictionResult object
    result = PredictionResult(
        request_hash="test",
        raw_response=raw_response
    )
    
    # Extract CIF data
    try:
        cif_data = result.extract_cif()
        print(f"✅ CIF data extracted: {len(cif_data)} characters")
    except Exception as e:
        print(f"❌ Failed to extract CIF: {e}")
        return
    
    # Extract confidence scores
    try:
        confidence_scores = result.get_confidence_scores()
        print(f"\n📊 Quality Metrics:")
        
        if confidence_scores:
            if 'mean' in confidence_scores:
                print(f"   Mean pLDDT: {confidence_scores['mean']:.1f}")
                print(f"   Standard Deviation: {confidence_scores['std']:.1f}")
                print(f"   Range: {confidence_scores['min']:.1f} - {confidence_scores['max']:.1f}")
                print(f"   Median: {confidence_scores['median']:.1f}")
                print(f"   Total Residues: {confidence_scores['count']}")
                
                if 'quality_breakdown' in confidence_scores:
                    breakdown = confidence_scores['quality_breakdown']
                    print(f"\n   Quality Distribution:")
                    print(f"     Very High Confidence (≥90): {breakdown['very_high_confidence_pct']:.1f}%")
                    print(f"     High Confidence (70-90): {breakdown['high_confidence_pct']:.1f}%")
                    print(f"     Medium Confidence (50-70): {breakdown['medium_confidence_pct']:.1f}%")
                    print(f"     Low Confidence (<50): {breakdown['low_confidence_pct']:.1f}%")
                
                # Quality assessment
                mean_plddt = confidence_scores['mean']
                if mean_plddt >= 90:
                    assessment = "Excellent"
                elif mean_plddt >= 80:
                    assessment = "Very Good"
                elif mean_plddt >= 70:
                    assessment = "Good"
                elif mean_plddt >= 60:
                    assessment = "Fair"
                else:
                    assessment = "Poor"
                
                print(f"\n   Overall Assessment: {assessment}")
                
                # Insights
                print(f"\n💡 Quality Insights:")
                if breakdown['very_high_confidence_pct'] > 70:
                    print("   • Excellent overall confidence - structure is highly reliable")
                elif breakdown['very_high_confidence_pct'] + breakdown['high_confidence_pct'] > 80:
                    print("   • Good overall confidence - structure is reliable")
                elif breakdown['low_confidence_pct'] > 30:
                    print("   • Some regions have low confidence - interpret with caution")
                
                if breakdown['low_confidence_pct'] > 50:
                    print("   • High proportion of low-confidence regions - consider experimental validation")
                
                if mean_plddt > 85:
                    print("   • High-quality prediction suitable for detailed analysis")
                elif mean_plddt > 70:
                    print("   • Good-quality prediction suitable for most applications")
                else:
                    print("   • Lower-quality prediction - use with caution")
                    
            else:
                print("   No detailed metrics available")
                print(f"   Raw scores: {confidence_scores}")
        else:
            print("   ❌ No confidence scores found")
            
    except Exception as e:
        print(f"❌ Failed to extract confidence scores: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_quality_extraction()
