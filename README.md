# BiolM Protein Design System

A comprehensive, scalable system for protein design workflows using the biolm API. This system transforms your basic protein prediction scripts into a powerful, organized platform for maximum insights extraction.

## Features

### 🚀 **Scalable Architecture**
- **Modular Design**: Clean separation of concerns with dedicated modules for API interaction, data management, workflow orchestration, and analysis
- **Robust Error Handling**: Comprehensive retry logic, caching, and graceful failure handling
- **Batch Processing**: Efficient handling of multiple predictions with rate limiting
- **Experiment Management**: Organized storage and tracking of all experiments and results

### 🧬 **Advanced Workflows**
- **Single Predictions**: Simple protein complex structure prediction
- **Parameter Optimization**: Systematic testing of different prediction parameters
- **Variant Design**: Automated generation and testing of protein sequence variants
- **Batch Predictions**: Parallel processing of multiple protein complexes
- **Template Workflows**: Pre-built workflows for common tasks (antibody design, optimization, etc.)

### 📊 **Comprehensive Analysis**
- **Automated Insights Extraction**: Intelligent analysis of prediction results
- **Confidence Score Analysis**: Statistical analysis of prediction confidence
- **Structure Metrics**: Extraction of key structural properties
- **Comparative Analysis**: Cross-experiment comparisons and trend analysis
- **Export Capabilities**: Easy data export for further analysis

### 💾 **Organized Data Management**
- **SQLite Database**: Efficient metadata storage and querying
- **File Organization**: Structured directory layout for results and artifacts
- **Caching System**: Intelligent caching to avoid redundant API calls
- **Backup Support**: Built-in data backup and export functionality

## Installation

1. **Clone or download the system files**
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
3. **Run setup**:
   ```bash
   python setup.py
   ```
4. **Configure your API token** in `biolm_config.json`:
   ```json
   {
     "api": {
       "token": "your_biolm_api_token_here"
     }
   }
   ```

## Quick Start

### Using the Python API

```python
from biolm_designer import BiolmDesigner

# Initialize the designer
designer = BiolmDesigner()

# Define your proteins
proteins = [
    {
        "name": "protein_A",
        "sequence": "EVQLLESGGGLVQPGGSLRLSCAASGLDISSVAMSWVRQAPGKGLEWVSAISPDGSYTYYADSVKGRFTISRDNSKNTLYLQMNSLRAEDTAVYYCAKGGGTLSKRTVWGQGTLVTVSS"
    },
    {
        "name": "protein_B",
        "sequence": "LYWDDLKRKLSEKLDSTDFTSTIKLLNENSYVPREAGSQKDENLALYVENEFREFKLSKVWRDQHFVKIQVKDSAQNSVIIVDKNGRLVYLVENPGGYVAYSKAATVTGKLVHANFGTKKDFEDLYTPVNGSIVIVRAGKITFAEKVANAESLNAIGVLIYMDQTKFPIVNAELSFFGHAHLGTGDPYTPGFPSFNHTQFPPSRSSGLPNIPVQTISRAAAEKLFGNMEGDCPSDWKTDSTCRMVTSESKNVKLTVSNVLKEIKILNIFGVIKGFVEPDHYVVVGAQRDAWGPGAAKSGVGTALLLKLAQMFSDMVLKDGFQPSRSIIFASWSAGDFGSVGATEWLEGYLSSLHLKAFTYINLDKAVLGTSNFKVSASPLLYTLIEKTMQNVKHPVTGQFLYQDSNWASKVEKLTLDNAAFPFLAYSGIPAVSFCFCEDTDYPYLGTTMDTYKELIERIPELNKVARAAAEVAGQFVIKLTHDVELNLDYEEYNSQLLSFVRDLNQYRADIKEMGLSLQWLYSARGDFFRATSRLTTDFGNAEKTDRFVMKKLNDRVMRVEYHFLSPYVSPKESPFRHVFWGSGSHTLPALLENLKLRKQNNGAFNETLFRNQLALATWTIQGAANALSGDVWDIDNEF"
    }
]

# Run a single prediction
result = designer.predict_single_complex(
    proteins=proteins,
    experiment_name="my_first_prediction"
)

print(f"Prediction completed! Run ID: {result['run_id']}")
```

### Using the Command Line

```bash
# Single prediction
python biolm_designer.py predict --proteins example_proteins.json --name "test_prediction"

# Batch prediction
python biolm_designer.py batch --protein-sets example_protein_sets.json --name "batch_test"

# Analyze results
python biolm_designer.py analyze --experiment-id "exp_20241209_143022_test_prediction"

# List experiments
python biolm_designer.py list --status completed
```

## Advanced Usage

### Parameter Optimization

```python
# Test different parameter combinations
parameter_grid = {
    "num_trunk_recycles": [2, 4, 6],
    "num_diffusion_timesteps": [100, 180, 250],
    "use_esm_embeddings": [True, False]
}

results = designer.optimize_parameters(
    proteins=proteins,
    parameter_grid=parameter_grid,
    experiment_name="parameter_optimization"
)
```

### Protein Variant Design

```python
# Design sequence variants
base_protein = {
    "name": "base_antibody",
    "sequence": "EVQLLESGGGLVQPGGSLRLSCAASGLDISSVAMSWVRQAPGKGLEWVSAISPDGSYTYYADSVKGRFTISRDNSKNTLYLQMNSLRAEDTAVYYCAKGGGTLSKRTVWGQGTLVTVSS"
}

mutations = [
    {"name": "V5A", "position": "5", "to": "A"},
    {"name": "L11F", "position": "11", "to": "F"},
    {"name": "S25T", "position": "25", "to": "T"}
]

results = designer.design_variants(
    base_protein=base_protein,
    mutations=mutations,
    experiment_name="variant_optimization"
)
```

### Antibody Design Workflow

```python
# Complete antibody design workflow
analysis = designer.run_antibody_design(
    antigen_sequence="YOUR_ANTIGEN_SEQUENCE",
    antibody_sequences=["ANTIBODY_1", "ANTIBODY_2"],
    experiment_name="therapeutic_antibody"
)
```

## System Architecture

```
biolm_designer.py     # Main interface and CLI
├── config.py         # Configuration management
├── biolm_client.py   # API client with retry logic
├── data_manager.py   # Data storage and organization
├── workflow.py       # Workflow orchestration
└── analysis.py       # Results analysis and insights
```

### Key Components

- **Config System**: Centralized configuration with environment variable support
- **API Client**: Robust client with caching, retry logic, and rate limiting
- **Data Manager**: SQLite-based metadata storage with file organization
- **Workflow Engine**: Orchestrates complex multi-step workflows
- **Analysis Engine**: Extracts insights and generates reports

## Configuration

The system uses a JSON configuration file (`biolm_config.json`) with the following structure:

```json
{
  "api": {
    "base_url": "https://biolm.ai/api/v3",
    "token": "your_token_here",
    "timeout": 300,
    "max_retries": 3,
    "retry_delay": 1.0
  },
  "chai": {
    "num_trunk_recycles": 4,
    "num_diffusion_timesteps": 180,
    "num_diffn_samples": 1,
    "use_esm_embeddings": true,
    "seed": 42
  },
  "storage": {
    "base_dir": "experiments",
    "results_dir": "results",
    "logs_dir": "logs",
    "cache_dir": "cache",
    "backup_dir": "backups"
  }
}
```

## Data Organization

```
experiments/
├── exp_20241209_143022_my_experiment/
│   ├── experiment.json           # Experiment metadata
│   └── results/
│       ├── run_xxx_result.json   # Raw API responses
│       └── run_xxx_structure.cif # Extracted structures
├── cache/                        # Cached API responses
├── logs/                         # System logs
└── experiments.db               # SQLite metadata database
```

## Examples

Run the comprehensive examples:

```bash
python examples.py
```

This will demonstrate:
- Single protein complex prediction
- Parameter optimization
- Variant design
- Batch processing
- Experiment analysis
- Antibody design workflow

## Migration from Your Current Setup

Your current files (`test.py`, `extract.py`) can be easily migrated:

1. **Replace `test.py`** with the new system:
   ```python
   from biolm_designer import BiolmDesigner
   designer = BiolmDesigner()
   # Your prediction logic here
   ```

2. **CIF extraction** is now automatic:
   ```python
   result = designer.predict_single_complex(proteins)
   # CIF data is automatically extracted and saved
   ```

3. **Organized results**: All outputs are now systematically organized and tracked

## Benefits Over Your Current Setup

### ✅ **Before (Current)**
- Manual API calls
- Basic error handling
- Manual file management
- No experiment tracking
- Limited insights extraction

### 🚀 **After (New System)**
- Robust API client with retry logic
- Comprehensive error handling and logging
- Automated data organization
- Full experiment lifecycle management
- Advanced analysis and insights extraction
- Scalable batch processing
- Parameter optimization capabilities
- Variant design workflows

## Troubleshooting

### Common Issues

1. **API Token Error**: Ensure your token is correctly set in `biolm_config.json`
2. **Permission Errors**: Check directory permissions for the experiments folder
3. **Network Issues**: The system will automatically retry failed requests
4. **Memory Issues**: Use batch processing for large datasets

### Logging

All operations are logged to `biolm_designer.log`. Check this file for detailed error information.

## Contributing

This system is designed to be extensible. You can:
- Add new workflow templates in `workflow.py`
- Extend analysis capabilities in `analysis.py`
- Add new API endpoints in `biolm_client.py`
- Customize configuration options in `config.py`

## License

This system is provided as-is for your protein design workflows. Modify and extend as needed for your research.
