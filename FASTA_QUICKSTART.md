# 🧬 FASTA Quick Start Guide
## Run Chai-1 Predictions with Your FASTA Files

### ⚡ **Super Simple Usage**

```bash
# 1. Predict structure from FASTA file
python fasta_predictor.py predict my_protein.fasta

# 2. List your predictions
python fasta_predictor.py list

# 3. Analyze results
python fasta_predictor.py analyze exp_20241209_143022_my_protein
```

That's it! Your results are automatically organized and trackable.

---

## 📁 **FASTA File Format**

Your FASTA files should look like this:

```fasta
>protein_name_1
MKLLNVINFVFLMFVSSSKILGSCQKDVNKDWRQFQKQEQGIDLYKHMFENYPPLRKYFKSREEYTAEDVQNDPFFAKQGQKILLACHVLCATYDDRETFNAYTRELLDRHARDHVHMPPEVWTDFWKLFEEYLGKKTTLDEPTKQAWHEIGREFAKEINKHGRVLVVNNAGIGILDDHHFNKFFKRQHINYLWNEQDMIAETEEHMHKTPGPGILSMANAGPNTNGSQFFICTAKTEWLDGKHVVFGKVKEGMNIVEAMERFGSRNGKTSKKITIADCGQLE

>protein_name_2
EVQLLESGGGLVQPGGSLRLSCAASGLDISSVAMSWVRQAPGKGLEWVSAISPDGSYTYYADSVKGRFTISRDNSKNTLYLQMNSLRAEDTAVYYCAKGGGTLSKRTVWGQGTLVTVSS
```

- Multiple proteins in one file = protein complex prediction
- Single protein in file = monomer prediction
- Protein names after `>` are used for tracking

---

## 🚀 **Examples**

### **Example 1: Single Protein Complex**
```bash
# Use the provided example
python fasta_predictor.py predict example_protein.fasta

# With custom name
python fasta_predictor.py predict example_protein.fasta --name "my_antibody_complex"
```

### **Example 2: Multiple Files (Batch)**
```bash
# Process multiple FASTA files at once
python fasta_predictor.py predict protein1.fasta protein2.fasta protein3.fasta
```

### **Example 3: Custom Parameters**
```bash
# Higher quality prediction (slower)
python fasta_predictor.py predict my_protein.fasta --params '{"num_trunk_recycles": 6, "num_diffusion_timesteps": 250}'

# Faster prediction
python fasta_predictor.py predict my_protein.fasta --params '{"num_trunk_recycles": 2, "num_diffusion_timesteps": 100}'
```

### **Example 4: Track Your Work**
```bash
# List recent predictions
python fasta_predictor.py list

# Analyze specific prediction
python fasta_predictor.py analyze exp_20241209_143022_my_experiment

# List more predictions
python fasta_predictor.py list --limit 20
```

### **Example 5: Quality Analysis**
```bash
# Generate detailed quality report
python quality_report.py report results/chai1_my_protein_summary.json

# List all available summaries
python quality_report.py list

# Compare multiple predictions
python quality_report.py compare results/prediction1_summary.json results/prediction2_summary.json
```

---

## 📊 **Quality Metrics Explained**

Your predictions now include comprehensive **pLDDT (predicted LDDT) scores** - the gold standard for protein structure confidence:

### **🎯 pLDDT Score Ranges**
- **Very High (≥90)**: Highly accurate, suitable for detailed analysis
- **High (70-90)**: Generally accurate, good for most applications
- **Medium (50-70)**: Moderately accurate, interpret with caution
- **Low (<50)**: Low accuracy, experimental validation recommended

### **📈 What You Get Automatically**
- **Mean pLDDT**: Overall prediction quality
- **Quality Distribution**: Percentage breakdown by confidence level
- **Per-residue Scores**: Individual confidence for each amino acid
- **Quality Assessment**: Overall rating (Excellent/Very Good/Good/Fair/Poor)
- **Actionable Insights**: Specific recommendations for your structure

### **🔍 Quality Report Example**
```
📊 Confidence Metrics (pLDDT):
   Mean: 89.5
   Range: 50.8 - 97.6

📈 Quality Distribution:
   Very High (≥90):  58.6% ███████████
   High (70-90):     39.8% ███████
   Medium (50-70):    1.6%
   Low (<50):         0.0%

🎯 Recommendations:
   ✅ Excellent prediction quality - suitable for:
      • Detailed structural analysis
      • Drug design applications
      • Protein-protein interaction studies
```

---

## 📊 **What You Get**

After running a prediction, you automatically get:

### **✅ Immediate Feedback with Quality Metrics**
```
🧬 FASTA Predictor for Chai-1 via BiolM API
==================================================
📄 Reading FASTA file: my_protein.fasta
✅ Found 2 protein(s):
   1. protein_A (127 residues)
   2. protein_B (639 residues)

🚀 Running Chai-1 prediction...
   Experiment: chai1_my_protein

✅ Prediction completed!
   Run ID: run_20241209_143045_123456
   Mean pLDDT: 89.5
   pLDDT Range: 50.8 - 97.6
   Quality Distribution:
     Very High (≥90): 58.6%
     High (70-90): 39.8%
     Medium (50-70): 1.6%
     Low (<50): 0.0%
   Summary saved: results/chai1_my_protein_summary.json
   Overall Quality: Very Good
```

### **📁 Organized Results**
```
experiments/
└── exp_20241209_143022_chai1_my_protein/
    ├── experiment.json              # Experiment metadata
    └── results/
        ├── run_xxx_result.json      # Raw API response
        └── run_xxx_structure.cif    # Your protein structure!

results/
└── chai1_my_protein_summary.json   # Quick summary
```

### **🔍 Trackable History**
```bash
python fasta_predictor.py list
```
```
📋 Recent Predictions (last 10):
--------------------------------------------------------------------------------
✅ chai1_my_protein
   ID: exp_20241209_143022_chai1_my_protein
   Created: 2024-12-09T14:30:22
   Status: completed
   Runs: 1 total
   Status breakdown: completed: 1
```

---

## 🎯 **Common Use Cases**

### **🧬 Antibody-Antigen Complex**
```fasta
>my_antibody
EVQLLESGGGLVQPGGSLRLSCAASGLDISSVAMSWVRQAPGKGLEWVSAISPDGSYTYYADSVKGRFTISRDNSKNTLYLQMNSLRAEDTAVYYCAKGGGTLSKRTVWGQGTLVTVSS
>target_antigen
LYWDDLKRKLSEKLDSTDFTSTIKLLNENSYVPREAGSQKDENLALYVENEFREFKLSKVWRDQHFVKIQVKDSAQNSVIIVDKNGRLVYLVENPGGYVAYSKAATVTGKLVHANFGTKKDFEDLYTPVNGSIVIVRAGKITFAEKVANAESLNAIGVLIYMDQTKFPIVNAELSFFGHAHLGTGDPYTPGFPSFNHTQFPPSRSSGLPNIPVQTISRAAAEKLFGNMEGDCPSDWKTDSTCRMVTSESKNVKLTVSNVLKEIKILNIFGVIKGFVEPDHYVVVGAQRDAWGPGAAKSGVGTALLLKLAQMFSDMVLKDGFQPSRSIIFASWSAGDFGSVGATEWLEGYLSSLHLKAFTYINLDKAVLGTSNFKVSASPLLYTLIEKTMQNVKHPVTGQFLYQDSNWASKVEKLTLDNAAFPFLAYSGIPAVSFCFCEDTDYPYLGTTMDTYKELIERIPELNKVARAAAEVAGQFVIKLTHDVELNLDYEEYNSQLLSFVRDLNQYRADIKEMGLSLQWLYSARGDFFRATSRLTTDFGNAEKTDRFVMKKLNDRVMRVEYHFLSPYVSPKESPFRHVFWGSGSHTLPALLENLKLRKQNNGAFNETLFRNQLALATWTIQGAANALSGDVWDIDNEF
```

### **🔬 Single Protein Design**
```fasta
>my_designed_enzyme
MKLLNVINFVFLMFVSSSKILGSCQKDVNKDWRQFQKQEQGIDLYKHMFENYPPLRKYFKSREEYTAEDVQNDPFFAKQGQKILLACHVLCATYDDRETFNAYTRELLDRHARDHVHMPPEVWTDFWKLFEEYLGKKTTLDEPTKQAWHEIGREFAKEINKHGRVLVVNNAGIGILDDHHFNKFFKRQHINYLWNEQDMIAETEEHMHKTPGPGILSMANAGPNTNGSQFFICTAKTEWLDGKHVVFGKVKEGMNIVEAMERFGSRNGKTSKKITIADCGQLE
```

### **🧪 Protein Complex Assembly**
```fasta
>chain_A
SEQUENCE_A_HERE
>chain_B  
SEQUENCE_B_HERE
>chain_C
SEQUENCE_C_HERE
```

---

## ⚙️ **Parameter Options**

You can customize predictions with `--params`:

```json
{
  "num_trunk_recycles": 4,        // More = higher quality (2-8)
  "num_diffusion_timesteps": 180, // More = higher quality (100-250)  
  "use_esm_embeddings": true,     // Usually keep true
  "seed": 42                      // For reproducibility
}
```

**Quick presets:**
- **Fast**: `'{"num_trunk_recycles": 2, "num_diffusion_timesteps": 100}'`
- **Default**: `'{"num_trunk_recycles": 4, "num_diffusion_timesteps": 180}'`
- **High Quality**: `'{"num_trunk_recycles": 6, "num_diffusion_timesteps": 250}'`

---

## 🔧 **Setup (One-time)**

1. **Make sure you have the system set up:**
   ```bash
   python setup.py
   pip install -r requirements.txt
   ```

2. **Add your API token to `biolm_config.json`:**
   ```json
   {
     "api": {
       "token": "your_biolm_api_token_here"
     }
   }
   ```

3. **Test with example:**
   ```bash
   python fasta_predictor.py predict example_protein.fasta
   ```

---

## 🎉 **That's It!**

You now have a **clear, easy, trackable** way to run Chai-1 predictions with your FASTA files. 

- ✅ **Clear**: Simple command-line interface
- ✅ **Easy**: Just point to your FASTA file  
- ✅ **Trackable**: All results organized and searchable

**Next steps:**
- Try with your own FASTA files
- Experiment with different parameters
- Use `list` and `analyze` commands to track your work
- Scale up to batch processing multiple files
