"""
Analysis and insights extraction for protein design results.
"""
import json
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import logging
from dataclasses import dataclass
import sqlite3

from data_manager import data_manager


@dataclass
class StructureMetrics:
    """Metrics extracted from protein structures."""
    num_residues: int
    num_chains: int
    resolution: Optional[float] = None
    confidence_scores: Dict[str, float] = None
    secondary_structure: Dict[str, float] = None
    
    def __post_init__(self):
        if self.confidence_scores is None:
            self.confidence_scores = {}
        if self.secondary_structure is None:
            self.secondary_structure = {}


@dataclass
class ComparisonResult:
    """Result of comparing two structures."""
    rmsd: Optional[float] = None
    tm_score: Optional[float] = None
    gdt_ts: Optional[float] = None
    sequence_identity: Optional[float] = None
    structural_similarity: Optional[float] = None


class StructureAnalyzer:
    """Analyzes protein structures and extracts insights."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def parse_cif_file(self, cif_path: Path) -> StructureMetrics:
        """Parse CIF file and extract comprehensive metrics including pLDDT scores."""
        try:
            with open(cif_path, 'r') as f:
                content = f.read()

            lines = content.split('\n')

            # Extract basic structure info
            atom_lines = [line for line in lines if line.startswith('ATOM')]

            if not atom_lines:
                return StructureMetrics(num_residues=0, num_chains=0)

            # Extract chain IDs and residue numbers
            chains = set()
            residues = set()

            for line in atom_lines:
                parts = line.split()
                if len(parts) >= 10:
                    chain_id = parts[9]  # Chain ID
                    res_num = parts[6]   # Residue number
                    chains.add(chain_id)
                    residues.add((chain_id, res_num))

            # Extract pLDDT confidence scores
            confidence_scores = self._extract_plddt_scores(lines)

            # Calculate confidence statistics
            if confidence_scores:
                conf_stats = {
                    "mean": np.mean(confidence_scores),
                    "std": np.std(confidence_scores),
                    "min": np.min(confidence_scores),
                    "max": np.max(confidence_scores),
                    "median": np.median(confidence_scores),
                    "q25": np.percentile(confidence_scores, 25),
                    "q75": np.percentile(confidence_scores, 75)
                }

                # Quality categories based on pLDDT ranges
                very_high = sum(1 for score in confidence_scores if score >= 90)
                high = sum(1 for score in confidence_scores if 70 <= score < 90)
                medium = sum(1 for score in confidence_scores if 50 <= score < 70)
                low = sum(1 for score in confidence_scores if score < 50)

                conf_stats["quality_breakdown"] = {
                    "very_high_confidence": {"count": very_high, "percentage": very_high/len(confidence_scores)*100},
                    "high_confidence": {"count": high, "percentage": high/len(confidence_scores)*100},
                    "medium_confidence": {"count": medium, "percentage": medium/len(confidence_scores)*100},
                    "low_confidence": {"count": low, "percentage": low/len(confidence_scores)*100}
                }
            else:
                conf_stats = {}

            return StructureMetrics(
                num_residues=len(residues),
                num_chains=len(chains),
                confidence_scores=conf_stats
            )

        except Exception as e:
            self.logger.error(f"Error parsing CIF file {cif_path}: {e}")
            return StructureMetrics(num_residues=0, num_chains=0)

    def _extract_plddt_scores(self, lines: List[str]) -> List[float]:
        """Extract pLDDT confidence scores from CIF file."""
        scores = []

        # Look for the pLDDT metric section
        in_plddt_section = False

        for line in lines:
            # Check if we're entering the pLDDT section
            if "_ma_qa_metric_local.metric_value" in line:
                in_plddt_section = True
                continue

            # Check if we're leaving the section
            if in_plddt_section and (line.startswith('#') or line.strip() == ''):
                break

            # Extract scores
            if in_plddt_section and line.strip():
                parts = line.split()
                if len(parts) >= 7:  # Ensure we have enough columns
                    try:
                        score = float(parts[6])  # metric_value is the 7th column (0-indexed: 6)
                        scores.append(score)
                    except (ValueError, IndexError):
                        continue

        return scores
    
    def extract_confidence_scores(self, result_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract confidence scores from prediction results."""
        scores = {}
        
        try:
            # This would need to be adapted based on actual API response structure
            if "confidence" in result_data:
                scores = result_data["confidence"]
            elif "scores" in result_data:
                scores = result_data["scores"]
            
            # Calculate average confidence if per-residue scores are available
            if isinstance(scores, list):
                scores = {"average": np.mean(scores)}
                
        except Exception as e:
            self.logger.warning(f"Could not extract confidence scores: {e}")
        
        return scores
    
    def compare_structures(self, cif_path1: Path, cif_path2: Path) -> ComparisonResult:
        """Compare two protein structures."""
        # This is a placeholder - would need actual structure comparison tools
        # like PyMOL, ChimeraX, or BioPython for real implementation
        
        try:
            metrics1 = self.parse_cif_file(cif_path1)
            metrics2 = self.parse_cif_file(cif_path2)
            
            # Simple sequence-based comparison
            seq_identity = self._calculate_sequence_identity(cif_path1, cif_path2)
            
            return ComparisonResult(sequence_identity=seq_identity)
            
        except Exception as e:
            self.logger.error(f"Error comparing structures: {e}")
            return ComparisonResult()
    
    def _calculate_sequence_identity(self, cif_path1: Path, cif_path2: Path) -> float:
        """Calculate sequence identity between two structures."""
        # Placeholder implementation
        return 0.0


class ExperimentAnalyzer:
    """Analyzes experiment results and generates insights."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.structure_analyzer = StructureAnalyzer()
    
    def analyze_experiment(self, experiment_id: str) -> Dict[str, Any]:
        """Analyze all results from an experiment."""
        experiment = data_manager.get_experiment(experiment_id)
        if not experiment:
            raise ValueError(f"Experiment {experiment_id} not found")
        
        # Get all runs for this experiment
        runs_data = self._get_experiment_runs(experiment_id)
        
        # Analyze each run
        run_analyses = []
        for run in runs_data:
            analysis = self._analyze_run(run)
            run_analyses.append(analysis)
        
        # Generate summary statistics
        summary = self._generate_experiment_summary(run_analyses)
        
        return {
            "experiment": experiment.to_dict(),
            "run_analyses": run_analyses,
            "summary": summary,
            "insights": self._extract_insights(run_analyses)
        }
    
    def _get_experiment_runs(self, experiment_id: str) -> List[Dict[str, Any]]:
        """Get all runs for an experiment."""
        runs = []
        with sqlite3.connect(data_manager.db_path) as conn:
            cursor = conn.execute("""
                SELECT id, request_hash, status, result_path, metadata
                FROM experiment_runs 
                WHERE experiment_id = ?
                ORDER BY created_at
            """, (experiment_id,))
            
            for row in cursor.fetchall():
                runs.append({
                    "id": row[0],
                    "request_hash": row[1],
                    "status": row[2],
                    "result_path": row[3],
                    "metadata": json.loads(row[4]) if row[4] else {}
                })
        
        return runs
    
    def _analyze_run(self, run_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze a single run."""
        analysis = {
            "run_id": run_data["id"],
            "status": run_data["status"],
            "metadata": run_data["metadata"]
        }
        
        if run_data["status"] == "completed" and run_data["result_path"]:
            try:
                # Load result data
                result_path = Path(run_data["result_path"])
                if result_path.exists():
                    with open(result_path, 'r') as f:
                        result_data = json.load(f)
                    
                    # Extract confidence scores
                    confidence_scores = self.structure_analyzer.extract_confidence_scores(result_data)
                    analysis["confidence_scores"] = confidence_scores
                    
                    # Analyze structure if CIF file exists
                    cif_path = result_path.parent / f"{run_data['id']}_structure.cif"
                    if cif_path.exists():
                        structure_metrics = self.structure_analyzer.parse_cif_file(cif_path)
                        analysis["structure_metrics"] = structure_metrics.__dict__
                
            except Exception as e:
                self.logger.error(f"Error analyzing run {run_data['id']}: {e}")
                analysis["error"] = str(e)
        
        return analysis
    
    def _generate_experiment_summary(self, run_analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary statistics for an experiment."""
        total_runs = len(run_analyses)
        successful_runs = [r for r in run_analyses if r["status"] == "completed"]
        failed_runs = [r for r in run_analyses if r["status"] == "failed"]
        
        summary = {
            "total_runs": total_runs,
            "successful_runs": len(successful_runs),
            "failed_runs": len(failed_runs),
            "success_rate": len(successful_runs) / total_runs if total_runs > 0 else 0
        }
        
        # Aggregate confidence scores
        confidence_scores = []
        for run in successful_runs:
            if "confidence_scores" in run and "average" in run["confidence_scores"]:
                confidence_scores.append(run["confidence_scores"]["average"])
        
        if confidence_scores:
            summary["confidence_stats"] = {
                "mean": np.mean(confidence_scores),
                "std": np.std(confidence_scores),
                "min": np.min(confidence_scores),
                "max": np.max(confidence_scores)
            }
        
        # Aggregate structure metrics
        structure_metrics = []
        for run in successful_runs:
            if "structure_metrics" in run:
                structure_metrics.append(run["structure_metrics"])
        
        if structure_metrics:
            summary["structure_stats"] = self._aggregate_structure_metrics(structure_metrics)
        
        return summary
    
    def _aggregate_structure_metrics(self, metrics_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Aggregate structure metrics across runs."""
        num_residues = [m["num_residues"] for m in metrics_list if "num_residues" in m]
        num_chains = [m["num_chains"] for m in metrics_list if "num_chains" in m]
        
        stats = {}
        if num_residues:
            stats["residues"] = {
                "mean": np.mean(num_residues),
                "std": np.std(num_residues),
                "min": np.min(num_residues),
                "max": np.max(num_residues)
            }
        
        if num_chains:
            stats["chains"] = {
                "mean": np.mean(num_chains),
                "std": np.std(num_chains),
                "min": np.min(num_chains),
                "max": np.max(num_chains)
            }
        
        return stats
    
    def _extract_insights(self, run_analyses: List[Dict[str, Any]]) -> List[str]:
        """Extract insights from experiment results."""
        insights = []
        
        successful_runs = [r for r in run_analyses if r["status"] == "completed"]
        
        if not successful_runs:
            insights.append("No successful predictions in this experiment.")
            return insights
        
        # Confidence score insights
        confidence_scores = []
        for run in successful_runs:
            if "confidence_scores" in run and "average" in run["confidence_scores"]:
                confidence_scores.append(run["confidence_scores"]["average"])
        
        if confidence_scores:
            avg_confidence = np.mean(confidence_scores)
            if avg_confidence > 0.8:
                insights.append("High confidence predictions achieved (>80%)")
            elif avg_confidence < 0.5:
                insights.append("Low confidence predictions (<50%) - consider parameter optimization")
        
        # Structure complexity insights
        structure_metrics = [r.get("structure_metrics", {}) for r in successful_runs]
        num_chains = [m.get("num_chains", 0) for m in structure_metrics]
        
        if num_chains:
            max_chains = max(num_chains)
            if max_chains > 2:
                insights.append(f"Complex multi-chain structures predicted (up to {max_chains} chains)")
        
        # Parameter optimization insights
        params_used = set()
        for run in run_analyses:
            if "metadata" in run and "params" in run["metadata"]:
                params_str = json.dumps(run["metadata"]["params"], sort_keys=True)
                params_used.add(params_str)
        
        if len(params_used) > 1:
            insights.append("Multiple parameter sets tested - consider analyzing parameter effects")
        
        return insights
    
    def compare_experiments(self, experiment_ids: List[str]) -> Dict[str, Any]:
        """Compare results across multiple experiments."""
        experiment_analyses = []
        
        for exp_id in experiment_ids:
            try:
                analysis = self.analyze_experiment(exp_id)
                experiment_analyses.append(analysis)
            except Exception as e:
                self.logger.error(f"Error analyzing experiment {exp_id}: {e}")
        
        # Generate comparison insights
        comparison = {
            "experiments": experiment_analyses,
            "comparison_insights": self._generate_comparison_insights(experiment_analyses)
        }
        
        return comparison
    
    def _generate_comparison_insights(self, analyses: List[Dict[str, Any]]) -> List[str]:
        """Generate insights from comparing multiple experiments."""
        insights = []
        
        if len(analyses) < 2:
            return ["Need at least 2 experiments for comparison"]
        
        # Compare success rates
        success_rates = [a["summary"]["success_rate"] for a in analyses if "summary" in a]
        if success_rates:
            best_idx = np.argmax(success_rates)
            worst_idx = np.argmin(success_rates)
            
            if success_rates[best_idx] > success_rates[worst_idx]:
                best_exp = analyses[best_idx]["experiment"]["name"]
                worst_exp = analyses[worst_idx]["experiment"]["name"]
                insights.append(f"Best success rate: {best_exp} ({success_rates[best_idx]:.1%})")
                insights.append(f"Worst success rate: {worst_exp} ({success_rates[worst_idx]:.1%})")
        
        return insights


# Global analyzer instance
analyzer = ExperimentAnalyzer()
