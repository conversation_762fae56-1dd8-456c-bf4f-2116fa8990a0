import requests

url = "https://biolm.ai/api/v3/chai1/predict/"
headers = {
    "Authorization": "Token 6bf627a20fc4d573c6db14e5b1e52d673565a877a5bd37e421247c1715c39275",
    "Content-Type": "application/json"
}
payload = {
      "params": {
        "num_trunk_recycles": 4,
        "num_diffusion_timesteps": 180,
        "num_diffn_samples": 1,
        "use_esm_embeddings": True,
        "seed": 42,
        "include": []
      },
      "items": [
        {
          "molecules": [
            {
              "name": "1CX8-7EOW-1CX8_7EOWvhh_1",
              "type": "protein",
              "sequence": "EVQLVESGGGLVQPGGSLRLSCAASGADFSTRPMGWFRQAPGKGRELVAAIDSDGIGTYYPDSVEGRFTISRDNAKRMVYLQMNSLRAEDTAVYYCAAQGSAVADTSLLRSASAYTAWGQGTQVTVSS"
            },
            {
              "name": "1CX8",
              "type": "protein",
              "sequence": "LYWDDLKRKLSEKLDSTDFTSTIKLLNENSYVPREAGSQKDENLALYVENEFREFKLSKVWRDQHFVKIQVKDSAQNSVIIVDKNGRLVYLVENPGGYVAYSKAATVTGKLVHANFGTKKDFEDLYTPVNGSIVIVRAGKITFAEKVANAESLNAIGVLIYMDQTKFPIVNAELSFFGHAHLGTGDPYTPGFPSFNHTQFPPSRSSGLPNIPVQTISRAAAEKLFGNMEGDCPSDWKTDSTCRMVTSESKNVKLTVSNVLKEIKILNIFGVIKGFVEPDHYVVVGAQRDAWGPGAAKSGVGTALLLKLAQMFSDMVLKDGFQPSRSIIFASWSAGDFGSVGATEWLEGYLSSLHLKAFTYINLDKAVLGTSNFKVSASPLLYTLIEKTMQNVKHPVTGQFLYQDSNWASKVEKLTLDNAAFPFLAYSGIPAVSFCFCEDTDYPYLGTTMDTYKELIERIPELNKVARAAAEVAGQFVIKLTHDVELNLDYEEYNSQLLSFVRDLNQYRADIKEMGLSLQWLYSARGDFFRATSRLTTDFGNAEKTDRFVMKKLNDRVMRVEYHFLSPYVSPKESPFRHVFWGSGSHTLPALLENLKLRKQNNGAFNETLFRNQLALATWTIQGAANALSGDVWDIDNEF"
            }
          ]
        }
      ]
    }

response = requests.post(url, headers=headers, json=payload)
print(response.json())

