#!/usr/bin/env python3
"""
Debug script to test API calls and compare with working test.py
"""
import requests
import json
import logging
from config import config
from biolm_client import client, Molecule, PredictionRequest

# Set up logging to see what's happening
logging.basicConfig(level=logging.INFO)

def test_direct_api_call():
    """Test direct API call like in test.py"""
    print("=== Testing Direct API Call (like test.py) ===")
    
    url = "https://biolm.ai/api/v3/chai1/predict/"
    headers = {
        "Authorization": "Token 6bf627a20fc4d573c6db14e5b1e52d673565a877a5bd37e421247c1715c39275",
        "Content-Type": "application/json"
    }
    
    # Use the same proteins from your FASTA file
    payload = {
        "params": {
            "num_trunk_recycles": 4,
            "num_diffusion_timesteps": 180,
            "num_diffn_samples": 1,
            "use_esm_embeddings": True,
            "seed": 42,
            "include": []
        },
        "items": [
            {
                "molecules": [
                    {
                        "name": "6WRV-5I19-6WRV_0",
                        "type": "protein",
                        "sequence": "EVQLLESGGGLVQPGGSLRLSCAASGLDLSSVAMSWVRQAPGKGLEWVSAISKDGKNTYYADSVKGRFTISRDNSKNTLYLQMNSLRAEDTAVYYCAKNAGEVNLHSLWGQGTLVTVSS"
                    },
                    {
                        "name": "TFR1",
                        "type": "protein",
                        "sequence": "RLYWDDLKRKLSEKLDSTDFTSTIKLLNENSYVPREAGSQKDENLALYVENQFREFKLSKVWRDQHFVKIQVKDSAQNSVIIVDKNGRLVYLVENPGGYVAYSKAATVTGKLVHANFGTKKDFEDLYTPVNGSIVIVRAGKITFAEKVANAESLNAIGVLIYMDQTKFPIVNAELSFFGHAHLGTGDPYTPGFPSFNHTQFPPSRSSGLPNIPVQTISRAAAEKLFGNMEGDCPSDWKTDSTCRMVTSESKNVKLTVSNVLKEIKILNIFGVIKGFVEPDHYVVVGAQRDAWGPGAAKSGVGTALLLKLAQMFSDMVLKDGFQPSRSIIFASWSAGDFGSVGATEWLEGYLSSLHLKAFTYINLDKAVLGTSNFKVSASPLLYTLIEKTMQNVKHPVTGQFLYQDSNWASKVEKLTLDNAAFPFLAYSGIPAVSFCFCEDTDYPYLGTTMDTYKELIERIPELNKVARAAAEVAGQFVIKLTHDVELNLDYERYNSQLLSFVRDLNQYRADIKEMGLSLQWLYSARGDFFRATSRLTTDFGNAEKTDRFVMKKLNDRVMRVEYHFLSPYVSPKESPFRHVFWGSGSHTLPALLENLKLRKQNNGAFNETLFRNQLALATWTIQGAANALSGDVWDIDNE"
                    }
                ]
            }
        ]
    }
    
    print(f"URL: {url}")
    print(f"Headers: {headers}")
    print(f"Payload keys: {list(payload.keys())}")
    print(f"Number of molecules: {len(payload['items'][0]['molecules'])}")
    
    try:
        print("Making request...")
        response = requests.post(url, headers=headers, json=payload, timeout=300)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Direct API call successful!")
            result = response.json()
            print(f"Response keys: {list(result.keys())}")
            return result
        else:
            print(f"❌ Direct API call failed: {response.status_code}")
            print(f"Response text: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Direct API call error: {e}")
        return None


def test_new_system_api_call():
    """Test API call through new system"""
    print("\n=== Testing New System API Call ===")
    
    # Load config
    config.load()
    print(f"Config token: {config.api.token[:20]}...")
    print(f"Config base_url: {config.api.base_url}")
    print(f"Config timeout: {config.api.timeout}")
    
    # Create molecules
    molecules = [
        Molecule(
            name="6WRV-5I19-6WRV_0",
            type="protein",
            sequence="EVQLLESGGGLVQPGGSLRLSCAASGLDLSSVAMSWVRQAPGKGLEWVSAISKDGKNTYYADSVKGRFTISRDNSKNTLYLQMNSLRAEDTAVYYCAKNAGEVNLHSLWGQGTLVTVSS"
        ),
        Molecule(
            name="TFR1",
            type="protein",
            sequence="RLYWDDLKRKLSEKLDSTDFTSTIKLLNENSYVPREAGSQKDENLALYVENQFREFKLSKVWRDQHFVKIQVKDSAQNSVIIVDKNGRLVYLVENPGGYVAYSKAATVTGKLVHANFGTKKDFEDLYTPVNGSIVIVRAGKITFAEKVANAESLNAIGVLIYMDQTKFPIVNAELSFFGHAHLGTGDPYTPGFPSFNHTQFPPSRSSGLPNIPVQTISRAAAEKLFGNMEGDCPSDWKTDSTCRMVTSESKNVKLTVSNVLKEIKILNIFGVIKGFVEPDHYVVVGAQRDAWGPGAAKSGVGTALLLKLAQMFSDMVLKDGFQPSRSIIFASWSAGDFGSVGATEWLEGYLSSLHLKAFTYINLDKAVLGTSNFKVSASPLLYTLIEKTMQNVKHPVTGQFLYQDSNWASKVEKLTLDNAAFPFLAYSGIPAVSFCFCEDTDYPYLGTTMDTYKELIERIPELNKVARAAAEVAGQFVIKLTHDVELNLDYERYNSQLLSFVRDLNQYRADIKEMGLSLQWLYSARGDFFRATSRLTTDFGNAEKTDRFVMKKLNDRVMRVEYHFLSPYVSPKESPFRHVFWGSGSHTLPALLENLKLRKQNNGAFNETLFRNQLALATWTIQGAANALSGDVWDIDNE"
        )
    ]
    
    # Create request
    request = PredictionRequest(molecules=molecules)
    print(f"Request data: {json.dumps(request.to_dict(), indent=2)}")
    
    try:
        print("Making request through new system...")
        result = client.predict_chai1(request)
        print("✅ New system API call successful!")
        print(f"Result keys: {list(result.__dict__.keys())}")
        return result
        
    except Exception as e:
        print(f"❌ New system API call error: {e}")
        return None


def compare_payloads():
    """Compare the payloads to see if there are differences"""
    print("\n=== Comparing Payloads ===")
    
    # Direct payload (working)
    direct_payload = {
        "params": {
            "num_trunk_recycles": 4,
            "num_diffusion_timesteps": 180,
            "num_diffn_samples": 1,
            "use_esm_embeddings": True,
            "seed": 42,
            "include": []
        },
        "items": [
            {
                "molecules": [
                    {
                        "name": "6WRV-5I19-6WRV_0",
                        "type": "protein",
                        "sequence": "EVQLLESGGGLVQPGGSLRLSCAASGLDLSSVAMSWVRQAPGKGLEWVSAISKDGKNTYYADSVKGRFTISRDNSKNTLYLQMNSLRAEDTAVYYCAKNAGEVNLHSLWGQGTLVTVSS"
                    },
                    {
                        "name": "TFR1",
                        "type": "protein",
                        "sequence": "RLYWDDLKRKLSEKLDSTDFTSTIKLLNENSYVPREAGSQKDENLALYVENQFREFKLSKVWRDQHFVKIQVKDSAQNSVIIVDKNGRLVYLVENPGGYVAYSKAATVTGKLVHANFGTKKDFEDLYTPVNGSIVIVRAGKITFAEKVANAESLNAIGVLIYMDQTKFPIVNAELSFFGHAHLGTGDPYTPGFPSFNHTQFPPSRSSGLPNIPVQTISRAAAEKLFGNMEGDCPSDWKTDSTCRMVTSESKNVKLTVSNVLKEIKILNIFGVIKGFVEPDHYVVVGAQRDAWGPGAAKSGVGTALLLKLAQMFSDMVLKDGFQPSRSIIFASWSAGDFGSVGATEWLEGYLSSLHLKAFTYINLDKAVLGTSNFKVSASPLLYTLIEKTMQNVKHPVTGQFLYQDSNWASKVEKLTLDNAAFPFLAYSGIPAVSFCFCEDTDYPYLGTTMDTYKELIERIPELNKVARAAAEVAGQFVIKLTHDVELNLDYERYNSQLLSFVRDLNQYRADIKEMGLSLQWLYSARGDFFRATSRLTTDFGNAEKTDRFVMKKLNDRVMRVEYHFLSPYVSPKESPFRHVFWGSGSHTLPALLENLKLRKQNNGAFNETLFRNQLALATWTIQGAANALSGDVWDIDNE"
                    }
                ]
            }
        ]
    }
    
    # New system payload
    molecules = [
        Molecule(
            name="6WRV-5I19-6WRV_0",
            type="protein",
            sequence="EVQLLESGGGLVQPGGSLRLSCAASGLDLSSVAMSWVRQAPGKGLEWVSAISKDGKNTYYADSVKGRFTISRDNSKNTLYLQMNSLRAEDTAVYYCAKNAGEVNLHSLWGQGTLVTVSS"
        ),
        Molecule(
            name="TFR1",
            type="protein",
            sequence="RLYWDDLKRKLSEKLDSTDFTSTIKLLNENSYVPREAGSQKDENLALYVENQFREFKLSKVWRDQHFVKIQVKDSAQNSVIIVDKNGRLVYLVENPGGYVAYSKAATVTGKLVHANFGTKKDFEDLYTPVNGSIVIVRAGKITFAEKVANAESLNAIGVLIYMDQTKFPIVNAELSFFGHAHLGTGDPYTPGFPSFNHTQFPPSRSSGLPNIPVQTISRAAAEKLFGNMEGDCPSDWKTDSTCRMVTSESKNVKLTVSNVLKEIKILNIFGVIKGFVEPDHYVVVGAQRDAWGPGAAKSGVGTALLLKLAQMFSDMVLKDGFQPSRSIIFASWSAGDFGSVGATEWLEGYLSSLHLKAFTYINLDKAVLGTSNFKVSASPLLYTLIEKTMQNVKHPVTGQFLYQDSNWASKVEKLTLDNAAFPFLAYSGIPAVSFCFCEDTDYPYLGTTMDTYKELIERIPELNKVARAAAEVAGQFVIKLTHDVELNLDYERYNSQLLSFVRDLNQYRADIKEMGLSLQWLYSARGDFFRATSRLTTDFGNAEKTDRFVMKKLNDRVMRVEYHFLSPYVSPKESPFRHVFWGSGSHTLPALLENLKLRKQNNGAFNETLFRNQLALATWTIQGAANALSGDVWDIDNE"
        )
    ]
    request = PredictionRequest(molecules=molecules)
    new_payload = request.to_dict()
    
    print("Direct payload:")
    print(json.dumps(direct_payload, indent=2))
    
    print("\nNew system payload:")
    print(json.dumps(new_payload, indent=2))
    
    # Check for differences
    if json.dumps(direct_payload, sort_keys=True) == json.dumps(new_payload, sort_keys=True):
        print("✅ Payloads are identical!")
    else:
        print("❌ Payloads differ!")


if __name__ == "__main__":
    print("🔍 Debugging BiolM API Calls")
    print("=" * 50)
    
    # Compare payloads first
    compare_payloads()
    
    # Test direct API call
    direct_result = test_direct_api_call()
    
    # Test new system
    new_result = test_new_system_api_call()
    
    print("\n" + "=" * 50)
    if direct_result and new_result:
        print("✅ Both methods work!")
    elif direct_result:
        print("⚠️  Only direct method works - issue with new system")
    elif new_result:
        print("⚠️  Only new system works - issue with direct method")
    else:
        print("❌ Both methods failed")
