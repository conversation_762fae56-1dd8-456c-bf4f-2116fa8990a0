"""
Example usage of the biolm protein design system.
"""
from biolm_designer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from config import config
import json


def example_single_prediction():
    """Example: Single protein complex prediction."""
    print("=== Single Protein Complex Prediction ===")
    
    designer = BiolmDesigner()
    
    # Define proteins (using your existing data)
    proteins = [
        {
            "name": "TfR1_antigen",
            "sequence": "EVQLLESGGGLVQPGGSLRLSCAASGLDISSVAMSWVRQAPGKGLEWVSAISPDGSYTYYADSVKGRFTISRDNSKNTLYLQMNSLRAEDTAVYYCAKGGGTLSKRTVWGQGTLVTVSS"
        },
        {
            "name": "1CX8_target",
            "sequence": "LYWDDLKRKLSEKLDSTDFTSTIKLLNENSYVPREAGSQKDENLALYVENEFREFKLSKVWRDQHFVKIQVKDSAQNSVIIVDKNGRLVYLVENPGGYVAYSKAATVTGKLVHANFGTKKDFEDLYTPVNGSIVIVRAGKITFAEKVANAESLNAIGVLIYMDQTKFPIVNAELSFFGHAHLGTGDPYTPGFPSFNHTQFPPSRSSGLPNIPVQTISRAAAEKLFGNMEGDCPSDWKTDSTCRMVTSESKNVKLTVSNVLKEIKILNIFGVIKGFVEPDHYVVVGAQRDAWGPGAAKSGVGTALLLKLAQMFSDMVLKDGFQPSRSIIFASWSAGDFGSVGATEWLEGYLSSLHLKAFTYINLDKAVLGTSNFKVSASPLLYTLIEKTMQNVKHPVTGQFLYQDSNWASKVEKLTLDNAAFPFLAYSGIPAVSFCFCEDTDYPYLGTTMDTYKELIERIPELNKVARAAAEVAGQFVIKLTHDVELNLDYEEYNSQLLSFVRDLNQYRADIKEMGLSLQWLYSARGDFFRATSRLTTDFGNAEKTDRFVMKKLNDRVMRVEYHFLSPYVSPKESPFRHVFWGSGSHTLPALLENLKLRKQNNGAFNETLFRNQLALATWTIQGAANALSGDVWDIDNEF"
        }
    ]
    
    try:
        result = designer.predict_single_complex(
            proteins=proteins,
            experiment_name="TfR1_1CX8_complex"
        )
        
        print(f"Prediction completed!")
        print(f"Run ID: {result['run_id']}")
        print(f"Confidence scores: {result.get('confidence_scores', 'N/A')}")
        print(f"CIF data length: {len(result['cif_data'])} characters")
        
        return result
        
    except Exception as e:
        print(f"Prediction failed: {e}")
        return None


def example_parameter_optimization():
    """Example: Parameter optimization for better results."""
    print("\n=== Parameter Optimization ===")
    
    designer = BiolmDesigner()
    
    # Same proteins as before
    proteins = [
        {
            "name": "antibody_variant",
            "sequence": "EVQLVESGGGLVQPGGSLRLSCAASGADFSTRPMGWFRQAPGKGRELVAAIDSDGIGTYYPDSVEGRFTISRDNAKRMVYLQMNSLRAEDTAVYYCAAQGSAVADTSLLRSASAYTAWGQGTQVTVSS"
        },
        {
            "name": "target_protein",
            "sequence": "LYWDDLKRKLSEKLDSTDFTSTIKLLNENSYVPREAGSQKDENLALYVENEFREFKLSKVWRDQHFVKIQVKDSAQNSVIIVDKNGRLVYLVENPGGYVAYSKAATVTGKLVHANFGTKKDFEDLYTPVNGSIVIVRAGKITFAEKVANAESLNAIGVLIYMDQTKFPIVNAELSFFGHAHLGTGDPYTPGFPSFNHTQFPPSRSSGLPNIPVQTISRAAAEKLFGNMEGDCPSDWKTDSTCRMVTSESKNVKLTVSNVLKEIKILNIFGVIKGFVEPDHYVVVGAQRDAWGPGAAKSGVGTALLLKLAQMFSDMVLKDGFQPSRSIIFASWSAGDFGSVGATEWLEGYLSSLHLKAFTYINLDKAVLGTSNFKVSASPLLYTLIEKTMQNVKHPVTGQFLYQDSNWASKVEKLTLDNAAFPFLAYSGIPAVSFCFCEDTDYPYLGTTMDTYKELIERIPELNKVARAAAEVAGQFVIKLTHDVELNLDYEEYNSQLLSFVRDLNQYRADIKEMGLSLQWLYSARGDFFRATSRLTTDFGNAEKTDRFVMKKLNDRVMRVEYHFLSPYVSPKESPFRHVFWGSGSHTLPALLENLKLRKQNNGAFNETLFRNQLALATWTIQGAANALSGDVWDIDNEF"
        }
    ]
    
    # Define parameter grid to test
    parameter_grid = {
        "num_trunk_recycles": [2, 4, 6],
        "num_diffusion_timesteps": [100, 180, 250],
        "use_esm_embeddings": [True, False]
    }
    
    try:
        results = designer.optimize_parameters(
            proteins=proteins,
            parameter_grid=parameter_grid,
            experiment_name="parameter_optimization_study"
        )
        
        print(f"Parameter optimization completed!")
        print(f"Tested {len(results)} parameter combinations")
        
        # Find best result based on confidence
        best_result = None
        best_confidence = 0
        
        for result in results:
            if "confidence_scores" in result and "average" in result["confidence_scores"]:
                confidence = result["confidence_scores"]["average"]
                if confidence > best_confidence:
                    best_confidence = confidence
                    best_result = result
        
        if best_result:
            print(f"Best parameters: {best_result['parameters']}")
            print(f"Best confidence: {best_confidence}")
        
        return results
        
    except Exception as e:
        print(f"Parameter optimization failed: {e}")
        return None


def example_variant_design():
    """Example: Design and test protein variants."""
    print("\n=== Protein Variant Design ===")
    
    designer = BiolmDesigner()
    
    # Base protein
    base_protein = {
        "name": "base_antibody",
        "sequence": "EVQLLESGGGLVQPGGSLRLSCAASGLDISSVAMSWVRQAPGKGLEWVSAISPDGSYTYYADSVKGRFTISRDNSKNTLYLQMNSLRAEDTAVYYCAKGGGTLSKRTVWGQGTLVTVSS"
    }
    
    # Define mutations to test (CDR region modifications)
    mutations = [
        {"name": "V5A", "position": "5", "to": "A"},
        {"name": "L11F", "position": "11", "to": "F"},
        {"name": "S25T", "position": "25", "to": "T"},
        {"name": "G26A", "position": "26", "to": "A"},
        {"name": "L27V", "position": "27", "to": "V"}
    ]
    
    try:
        results = designer.design_variants(
            base_protein=base_protein,
            mutations=mutations,
            experiment_name="antibody_variant_design"
        )
        
        print(f"Variant design completed!")
        print(f"Tested {len(mutations)} variants")
        
        # Analyze results
        for result in results:
            if "error" not in result:
                mutation = result["mutation"]["name"]
                confidence = result.get("confidence_scores", {}).get("average", "N/A")
                print(f"Variant {mutation}: confidence = {confidence}")
        
        return results
        
    except Exception as e:
        print(f"Variant design failed: {e}")
        return None


def example_batch_prediction():
    """Example: Batch prediction for multiple complexes."""
    print("\n=== Batch Prediction ===")
    
    designer = BiolmDesigner()
    
    # Define multiple protein sets
    protein_sets = [
        [
            {"name": "complex1_A", "sequence": "MKLLNVINFVFLMFVSSSKILGSCQKDVNKDWRQFQKQEQGIDLYKHMFENYPPLRKYFKSREEYTAEDVQNDPFFAKQGQKILLACHVLCATYDDRETFNAYTRELLDRHARDHVHMPPEVWTDFWKLFEEYLGKKTTLDEPTKQAWHEIGREFAKEINKHGRVLVVNNAGIGILDDHHFNKFFKRQHINYLWNEQDMIAETEEHMHKTPGPGILSMANAGPNTNGSQFFICTAKTEWLDGKHVVFGKVKEGMNIVEAMERFGSRNGKTSKKITIADCGQLE"},
            {"name": "complex1_B", "sequence": "MKKLLFTCLLLVVSSGSAFSSCQKDVNKDWRQFQKQEQGIDLYKHMFENYPPLRKYFKSREEYTAEDVQNDPFFAKQGQKILLACHVLCATYDDRETFNAYTRELLDRHARDHVHMPPEVWTDFWKLFEEYLGKKTTLDEPTKQAWHEIGREFAKEINKHGRVLVVNNAGIGILDDHHFNKFFKRQHINYLWNEQDMIAETEEHMHKTPGPGILSMANAGPNTNGSQFFICTAKTEWLDGKHVVFGKVKEGMNIVEAMERFGSRNGKTSKKITIADCGQLE"}
        ],
        [
            {"name": "complex2_A", "sequence": "EVQLVESGGGLVQPGGSLRLSCAASGFTFSSYAMSWVRQAPGKGLEWVSAISGSGGSTYYADSVKGRFTISRDNSKNTLYLQMNSLRAEDTAVYYCAKVSYLSTASSLDYWGQGTLVTVSS"},
            {"name": "complex2_B", "sequence": "DIQMTQSPSSLSASVGDRVTITCRASQDVNTAVAWYQQKPGKAPKLLIYSASFLYSGVPSRFSGSRSGTDFTLTISSLQPEDFATYYCQQHYTTPPTFGQGTKVEIKRTVAAPSVFIFPPSDEQLKSGTASVVCLLNNFYPREAKVQWKVDNALQSGNSQESVTEQDSKDSTYSLSSTLTLSKADYEKHKVYACEVTHQGLSSPVTKSFNRGEC"}
        ]
    ]
    
    try:
        results = designer.predict_batch(
            protein_sets=protein_sets,
            experiment_name="multi_complex_batch"
        )
        
        print(f"Batch prediction completed!")
        print(f"Processed {len(protein_sets)} complexes")
        
        for i, result in enumerate(results):
            if "error" not in result:
                print(f"Complex {i+1}: Run ID = {result['run_id']}")
            else:
                print(f"Complex {i+1}: Failed - {result['error']}")
        
        return results
        
    except Exception as e:
        print(f"Batch prediction failed: {e}")
        return None


def example_experiment_analysis():
    """Example: Analyze experiment results."""
    print("\n=== Experiment Analysis ===")
    
    designer = BiolmDesigner()
    
    # List recent experiments
    experiments = designer.list_experiments()
    
    if not experiments:
        print("No experiments found. Run some predictions first!")
        return
    
    # Analyze the most recent experiment
    latest_exp = experiments[0]
    exp_id = latest_exp["id"]
    
    print(f"Analyzing experiment: {latest_exp['name']} ({exp_id})")
    
    try:
        analysis = designer.analyze_experiment(exp_id)
        
        print(f"Experiment Summary:")
        print(f"- Total runs: {analysis['summary']['total_runs']}")
        print(f"- Successful runs: {analysis['summary']['successful_runs']}")
        print(f"- Success rate: {analysis['summary']['success_rate']:.1%}")
        
        if "confidence_stats" in analysis["summary"]:
            conf_stats = analysis["summary"]["confidence_stats"]
            print(f"- Average confidence: {conf_stats['mean']:.3f} ± {conf_stats['std']:.3f}")
        
        print(f"Insights:")
        for insight in analysis["insights"]:
            print(f"- {insight}")
        
        return analysis
        
    except Exception as e:
        print(f"Analysis failed: {e}")
        return None


def example_antibody_design_workflow():
    """Example: Complete antibody design workflow."""
    print("\n=== Antibody Design Workflow ===")
    
    designer = BiolmDesigner()
    
    # Antigen sequence
    antigen_sequence = "LYWDDLKRKLSEKLDSTDFTSTIKLLNENSYVPREAGSQKDENLALYVENEFREFKLSKVWRDQHFVKIQVKDSAQNSVIIVDKNGRLVYLVENPGGYVAYSKAATVTGKLVHANFGTKKDFEDLYTPVNGSIVIVRAGKITFAEKVANAESLNAIGVLIYMDQTKFPIVNAELSFFGHAHLGTGDPYTPGFPSFNHTQFPPSRSSGLPNIPVQTISRAAAEKLFGNMEGDCPSDWKTDSTCRMVTSESKNVKLTVSNVLKEIKILNIFGVIKGFVEPDHYVVVGAQRDAWGPGAAKSGVGTALLLKLAQMFSDMVLKDGFQPSRSIIFASWSAGDFGSVGATEWLEGYLSSLHLKAFTYINLDKAVLGTSNFKVSASPLLYTLIEKTMQNVKHPVTGQFLYQDSNWASKVEKLTLDNAAFPFLAYSGIPAVSFCFCEDTDYPYLGTTMDTYKELIERIPELNKVARAAAEVAGQFVIKLTHDVELNLDYEEYNSQLLSFVRDLNQYRADIKEMGLSLQWLYSARGDFFRATSRLTTDFGNAEKTDRFVMKKLNDRVMRVEYHFLSPYVSPKESPFRHVFWGSGSHTLPALLENLKLRKQNNGAFNETLFRNQLALATWTIQGAANALSGDVWDIDNEF"
    
    # Candidate antibody sequences
    antibody_sequences = [
        "EVQLLESGGGLVQPGGSLRLSCAASGLDISSVAMSWVRQAPGKGLEWVSAISPDGSYTYYADSVKGRFTISRDNSKNTLYLQMNSLRAEDTAVYYCAKGGGTLSKRTVWGQGTLVTVSS",
        "EVQLVESGGGLVQPGGSLRLSCAASGADFSTRPMGWFRQAPGKGRELVAAIDSDGIGTYYPDSVEGRFTISRDNAKRMVYLQMNSLRAEDTAVYYCAAQGSAVADTSLLRSASAYTAWGQGTQVTVSS"
    ]
    
    try:
        analysis = designer.run_antibody_design(
            antigen_sequence=antigen_sequence,
            antibody_sequences=antibody_sequences,
            experiment_name="therapeutic_antibody_design"
        )
        
        print(f"Antibody design workflow completed!")
        print(f"Experiment ID: {analysis['experiment']['id']}")
        print(f"Results summary: {analysis['summary']}")
        
        return analysis
        
    except Exception as e:
        print(f"Antibody design workflow failed: {e}")
        return None


def main():
    """Run all examples."""
    print("BiolM Protein Design System - Examples")
    print("=" * 50)
    
    # Set up configuration with your API token
    config.api.token = "6bf627a20fc4d573c6db14e5b1e52d673565a877a5bd37e421247c1715c39275"
    
    # Run examples
    examples = [
        example_single_prediction,
        example_parameter_optimization,
        example_variant_design,
        example_batch_prediction,
        example_experiment_analysis,
        example_antibody_design_workflow
    ]
    
    for example_func in examples:
        try:
            example_func()
        except Exception as e:
            print(f"Example {example_func.__name__} failed: {e}")
        
        print("\n" + "-" * 50 + "\n")


if __name__ == "__main__":
    main()
