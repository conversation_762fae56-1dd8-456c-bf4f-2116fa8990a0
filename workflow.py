"""
Workflow orchestration for protein design experiments.
"""
import logging
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from pathlib import Path
import json

from config import config
from biolm_client import client, Molecule, PredictionRequest
from data_manager import data_manager, Experiment, ExperimentRun


@dataclass
class WorkflowStep:
    """Represents a step in a workflow."""
    name: str
    function: Callable
    params: Dict[str, Any]
    depends_on: List[str] = None
    
    def __post_init__(self):
        if self.depends_on is None:
            self.depends_on = []


class ProteinDesignWorkflow:
    """Orchestrates protein design workflows."""
    
    def __init__(self, experiment_name: str, description: str = ""):
        self.experiment = data_manager.create_experiment(experiment_name, description)
        self.logger = logging.getLogger(__name__)
        self.steps = []
        self.results = {}
    
    def add_step(self, step: WorkflowStep) -> None:
        """Add a step to the workflow."""
        self.steps.append(step)
    
    def run_single_prediction(self, proteins: List[Dict[str, str]], 
                            params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Run a single protein structure prediction."""
        self.logger.info(f"Running prediction for {len(proteins)} proteins")
        
        # Create molecules
        molecules = [
            Molecule(name=p["name"], type="protein", sequence=p["sequence"])
            for p in proteins
        ]
        
        # Create request
        request = PredictionRequest(molecules=molecules, params=params)
        
        # Add run to experiment
        run = data_manager.add_experiment_run(self.experiment.id, request)
        
        try:
            # Update run status
            data_manager.update_experiment_status(self.experiment.id, "running")
            
            # Make prediction
            result = client.predict_chai1(request)
            
            # Save result
            data_manager.update_run_result(run.id, result)
            
            self.logger.info(f"Prediction completed for run {run.id}")
            
            return {
                "run_id": run.id,
                "result": result,
                "cif_data": result.extract_cif(),
                "confidence_scores": result.get_confidence_scores()
            }
            
        except Exception as e:
            self.logger.error(f"Prediction failed for run {run.id}: {e}")
            # Update run with error
            with data_manager.db_path.open() as conn:
                conn.execute("""
                    UPDATE experiment_runs 
                    SET status = 'failed', error_message = ?
                    WHERE id = ?
                """, (str(e), run.id))
            raise
    
    def run_batch_predictions(self, protein_sets: List[List[Dict[str, str]]], 
                            params_list: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """Run multiple predictions in batch."""
        self.logger.info(f"Running batch predictions for {len(protein_sets)} sets")
        
        if params_list is None:
            params_list = [None] * len(protein_sets)
        
        results = []
        for i, (proteins, params) in enumerate(zip(protein_sets, params_list)):
            try:
                result = self.run_single_prediction(proteins, params)
                results.append(result)
                self.logger.info(f"Completed batch prediction {i + 1}/{len(protein_sets)}")
            except Exception as e:
                self.logger.error(f"Failed batch prediction {i + 1}: {e}")
                results.append({"error": str(e)})
        
        return results
    
    def run_parameter_sweep(self, proteins: List[Dict[str, str]], 
                          parameter_grid: Dict[str, List[Any]]) -> List[Dict[str, Any]]:
        """Run predictions with different parameter combinations."""
        from itertools import product
        
        # Generate all parameter combinations
        param_names = list(parameter_grid.keys())
        param_values = list(parameter_grid.values())
        
        combinations = []
        for combo in product(*param_values):
            param_dict = dict(zip(param_names, combo))
            combinations.append(param_dict)
        
        self.logger.info(f"Running parameter sweep with {len(combinations)} combinations")
        
        results = []
        for i, params in enumerate(combinations):
            try:
                result = self.run_single_prediction(proteins, params)
                result["parameters"] = params
                results.append(result)
                self.logger.info(f"Completed parameter combination {i + 1}/{len(combinations)}")
            except Exception as e:
                self.logger.error(f"Failed parameter combination {i + 1}: {e}")
                results.append({"parameters": params, "error": str(e)})
        
        return results
    
    def run_sequence_variants(self, base_protein: Dict[str, str], 
                            mutations: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """Run predictions for sequence variants."""
        self.logger.info(f"Running predictions for {len(mutations)} sequence variants")
        
        results = []
        base_sequence = base_protein["sequence"]
        
        for i, mutation in enumerate(mutations):
            try:
                # Apply mutation
                mutated_sequence = self._apply_mutation(base_sequence, mutation)
                
                # Create variant protein
                variant_protein = {
                    "name": f"{base_protein['name']}_{mutation['name']}",
                    "sequence": mutated_sequence
                }
                
                # Run prediction
                result = self.run_single_prediction([variant_protein])
                result["mutation"] = mutation
                results.append(result)
                
                self.logger.info(f"Completed variant {i + 1}/{len(mutations)}")
                
            except Exception as e:
                self.logger.error(f"Failed variant {i + 1}: {e}")
                results.append({"mutation": mutation, "error": str(e)})
        
        return results
    
    def _apply_mutation(self, sequence: str, mutation: Dict[str, str]) -> str:
        """Apply a point mutation to a sequence."""
        position = int(mutation["position"]) - 1  # Convert to 0-based
        new_aa = mutation["to"]
        
        if position < 0 or position >= len(sequence):
            raise ValueError(f"Invalid mutation position: {mutation['position']}")
        
        sequence_list = list(sequence)
        sequence_list[position] = new_aa
        return "".join(sequence_list)
    
    def run_complex_assembly(self, protein_chains: List[Dict[str, str]], 
                           assembly_configs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Run predictions for different protein complex assemblies."""
        self.logger.info(f"Running complex assembly predictions for {len(assembly_configs)} configurations")
        
        results = []
        for i, config in enumerate(assembly_configs):
            try:
                # Select chains based on config
                selected_chains = []
                for chain_name in config["chains"]:
                    chain = next((c for c in protein_chains if c["name"] == chain_name), None)
                    if chain:
                        selected_chains.append(chain)
                    else:
                        raise ValueError(f"Chain {chain_name} not found")
                
                # Run prediction
                result = self.run_single_prediction(selected_chains, config.get("params"))
                result["assembly_config"] = config
                results.append(result)
                
                self.logger.info(f"Completed assembly {i + 1}/{len(assembly_configs)}")
                
            except Exception as e:
                self.logger.error(f"Failed assembly {i + 1}: {e}")
                results.append({"assembly_config": config, "error": str(e)})
        
        return results
    
    def finalize(self) -> None:
        """Finalize the workflow and update experiment status."""
        data_manager.update_experiment_status(self.experiment.id, "completed")
        self.logger.info(f"Workflow completed for experiment {self.experiment.id}")
    
    def get_summary(self) -> Dict[str, Any]:
        """Get workflow summary."""
        # Get all runs for this experiment
        with data_manager.db_path.open() as conn:
            cursor = conn.execute("""
                SELECT status, COUNT(*) FROM experiment_runs 
                WHERE experiment_id = ? 
                GROUP BY status
            """, (self.experiment.id,))
            
            status_counts = dict(cursor.fetchall())
        
        return {
            "experiment_id": self.experiment.id,
            "experiment_name": self.experiment.name,
            "status_counts": status_counts,
            "total_runs": sum(status_counts.values())
        }


class WorkflowTemplates:
    """Pre-defined workflow templates for common tasks."""
    
    @staticmethod
    def antibody_design(antigen_sequence: str, antibody_sequences: List[str]) -> ProteinDesignWorkflow:
        """Template for antibody-antigen complex prediction."""
        workflow = ProteinDesignWorkflow("antibody_design", "Antibody-antigen complex prediction")
        
        proteins = [{"name": "antigen", "sequence": antigen_sequence}]
        for i, ab_seq in enumerate(antibody_sequences):
            proteins.append({"name": f"antibody_{i}", "sequence": ab_seq})
        
        workflow.run_single_prediction(proteins)
        return workflow
    
    @staticmethod
    def protein_optimization(base_sequence: str, optimization_positions: List[int]) -> ProteinDesignWorkflow:
        """Template for protein sequence optimization."""
        workflow = ProteinDesignWorkflow("protein_optimization", "Protein sequence optimization")
        
        # Generate mutations for each position
        amino_acids = "ACDEFGHIKLMNPQRSTVWY"
        mutations = []
        
        for pos in optimization_positions:
            for aa in amino_acids:
                mutations.append({
                    "name": f"pos{pos}to{aa}",
                    "position": str(pos),
                    "to": aa
                })
        
        base_protein = {"name": "base_protein", "sequence": base_sequence}
        workflow.run_sequence_variants(base_protein, mutations)
        return workflow
    
    @staticmethod
    def complex_stability_screen(protein_chains: List[Dict[str, str]]) -> ProteinDesignWorkflow:
        """Template for screening protein complex stability."""
        workflow = ProteinDesignWorkflow("complex_stability", "Protein complex stability screening")
        
        # Test different parameter combinations for stability
        parameter_grid = {
            "num_trunk_recycles": [2, 4, 6],
            "num_diffusion_timesteps": [100, 180, 250]
        }
        
        workflow.run_parameter_sweep(protein_chains, parameter_grid)
        return workflow
