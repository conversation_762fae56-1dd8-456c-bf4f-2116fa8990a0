import ast, pathlib

in_path  = "1CX8-7EOW-1CX8_7EOWvhh_1_chai1.out"   # your upload
out_path = "1CX8-7EOW-1CX8_7EOWvhh_1_chai1_extracted.cif"

# read the wrapper
wrapper = pathlib.Path(in_path).read_text()
data    = ast.literal_eval(wrapper)          # converts the string → Python dict
mmcif   = data["results"][0][0]["cif"]       # pull out the mmCIF text

pathlib.Path(out_path).write_text(mmcif)     # save as plain mmCIF
print(f"clean mmCIF saved to {out_path}")
