"""
Configuration management for biolm protein design workflows.
"""
import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime


@dataclass
class APIConfig:
    """Configuration for biolm API."""
    base_url: str = "https://biolm.ai/api/v3"
    token: str = ""
    timeout: int = 300
    max_retries: int = 3
    retry_delay: float = 1.0


@dataclass
class ChaiConfig:
    """Configuration for Chai-1 predictions."""
    num_trunk_recycles: int = 4
    num_diffusion_timesteps: int = 180
    num_diffn_samples: int = 1
    use_esm_embeddings: bool = True
    seed: Optional[int] = 42
    include: list = None

    def __post_init__(self):
        if self.include is None:
            self.include = []


@dataclass
class StorageConfig:
    """Configuration for data storage."""
    base_dir: str = "experiments"
    results_dir: str = "results"
    logs_dir: str = "logs"
    cache_dir: str = "cache"
    backup_dir: str = "backups"


@dataclass
class ExperimentConfig:
    """Configuration for experiments."""
    name: str = ""
    description: str = ""
    tags: list = None
    metadata: dict = None

    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.metadata is None:
            self.metadata = {}


class Config:
    """Main configuration manager."""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "biolm_config.json"
        self.api = APIConfig()
        self.chai = ChaiConfig()
        self.storage = StorageConfig()
        self.experiment = ExperimentConfig()
        
        # Load from file if exists
        self.load()
        
        # Override with environment variables
        self._load_from_env()
    
    def load(self) -> None:
        """Load configuration from file."""
        if Path(self.config_file).exists():
            with open(self.config_file, 'r') as f:
                data = json.load(f)
                
            if 'api' in data:
                self.api = APIConfig(**data['api'])
            if 'chai' in data:
                self.chai = ChaiConfig(**data['chai'])
            if 'storage' in data:
                self.storage = StorageConfig(**data['storage'])
            if 'experiment' in data:
                self.experiment = ExperimentConfig(**data['experiment'])
    
    def save(self) -> None:
        """Save configuration to file."""
        data = {
            'api': asdict(self.api),
            'chai': asdict(self.chai),
            'storage': asdict(self.storage),
            'experiment': asdict(self.experiment)
        }
        
        with open(self.config_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    def _load_from_env(self) -> None:
        """Load configuration from environment variables."""
        # API configuration
        if token := os.getenv('BIOLM_API_TOKEN'):
            self.api.token = token
        if base_url := os.getenv('BIOLM_BASE_URL'):
            self.api.base_url = base_url
        if timeout := os.getenv('BIOLM_TIMEOUT'):
            self.api.timeout = int(timeout)
        
        # Storage configuration
        if base_dir := os.getenv('BIOLM_BASE_DIR'):
            self.storage.base_dir = base_dir
    
    def get_experiment_dir(self, experiment_name: str) -> Path:
        """Get experiment directory path."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        exp_dir = Path(self.storage.base_dir) / f"{experiment_name}_{timestamp}"
        return exp_dir
    
    def ensure_directories(self) -> None:
        """Ensure all required directories exist."""
        base_path = Path(self.storage.base_dir)
        for dir_name in [self.storage.results_dir, self.storage.logs_dir, 
                        self.storage.cache_dir, self.storage.backup_dir]:
            (base_path / dir_name).mkdir(parents=True, exist_ok=True)
    
    def validate(self) -> bool:
        """Validate configuration."""
        if not self.api.token:
            raise ValueError("API token is required")
        if not self.api.base_url:
            raise ValueError("API base URL is required")
        return True


# Global configuration instance
config = Config()
