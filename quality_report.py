#!/usr/bin/env python3
"""
Generate comprehensive quality reports for protein structure predictions.
"""
import json
import argparse
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

def generate_quality_report(summary_file: str) -> None:
    """Generate a comprehensive quality report from a summary file."""
    
    summary_path = Path(summary_file)
    if not summary_path.exists():
        print(f"❌ Summary file not found: {summary_file}")
        return
    
    # Load summary
    with open(summary_path, 'r') as f:
        summary = json.load(f)
    
    print("🧬 Protein Structure Quality Report")
    print("=" * 60)
    
    # Basic info
    print(f"Experiment: {summary.get('experiment_name', 'Unknown')}")
    print(f"FASTA File: {summary.get('fasta_file', 'Unknown')}")
    print(f"Run ID: {summary.get('run_id', 'Unknown')}")
    print(f"Timestamp: {summary.get('timestamp', 'Unknown')}")
    
    # Overall assessment
    quality = summary.get('quality_assessment', 'Unknown')
    quality_emoji = {
        'Excellent': '🟢',
        'Very Good': '🟢', 
        'Good': '🟡',
        'Fair': '🟠',
        'Poor': '🔴',
        'Unknown': '⚪'
    }
    
    print(f"\n{quality_emoji.get(quality, '⚪')} Overall Quality: {quality}")
    
    # Confidence scores
    conf = summary.get('confidence_scores', {})
    if conf and 'mean' in conf:
        print(f"\n📊 Confidence Metrics (pLDDT):")
        print(f"   Mean: {conf['mean']:.1f}")
        print(f"   Median: {conf['median']:.1f}")
        print(f"   Standard Deviation: {conf['std']:.1f}")
        print(f"   Range: {conf['min']:.1f} - {conf['max']:.1f}")
        print(f"   Total Residues: {conf['count']}")
        
        # Quality breakdown
        if 'quality_breakdown' in conf:
            breakdown = conf['quality_breakdown']
            print(f"\n📈 Quality Distribution:")
            
            # Create visual bar
            very_high = breakdown['very_high_confidence_pct']
            high = breakdown['high_confidence_pct'] 
            medium = breakdown['medium_confidence_pct']
            low = breakdown['low_confidence_pct']
            
            print(f"   Very High (≥90): {very_high:5.1f}% {'█' * int(very_high/5)}")
            print(f"   High (70-90):    {high:5.1f}% {'█' * int(high/5)}")
            print(f"   Medium (50-70):  {medium:5.1f}% {'█' * int(medium/5)}")
            print(f"   Low (<50):       {low:5.1f}% {'█' * int(low/5)}")
            
            # Confidence score interpretation
            print(f"\n📋 Interpretation Guide:")
            print(f"   Very High (≥90): Highly accurate, suitable for detailed analysis")
            print(f"   High (70-90):    Generally accurate, good for most applications")
            print(f"   Medium (50-70):  Moderately accurate, interpret with caution")
            print(f"   Low (<50):       Low accuracy, experimental validation recommended")
    
    # Structure info
    struct_info = summary.get('structure_info', {})
    if struct_info:
        print(f"\n🏗️  Structure Information:")
        print(f"   CIF File Size: {struct_info.get('cif_length', 0):,} characters")
        print(f"   Structure Available: {'✅' if struct_info.get('has_structure', False) else '❌'}")
    
    # Quality insights
    insights = summary.get('quality_insights', [])
    if insights:
        print(f"\n💡 Quality Insights:")
        for insight in insights:
            print(f"   • {insight}")
    
    # Recommendations
    print(f"\n🎯 Recommendations:")
    
    if conf and 'mean' in conf:
        mean_plddt = conf['mean']
        breakdown = conf.get('quality_breakdown', {})
        
        if mean_plddt >= 85:
            print("   ✅ Excellent prediction quality - suitable for:")
            print("      • Detailed structural analysis")
            print("      • Drug design applications")
            print("      • Protein-protein interaction studies")
            print("      • Publication-quality figures")
        elif mean_plddt >= 70:
            print("   ✅ Good prediction quality - suitable for:")
            print("      • General structural analysis")
            print("      • Functional annotation")
            print("      • Comparative studies")
            print("      • Initial drug screening")
        else:
            print("   ⚠️  Lower prediction quality - consider:")
            print("      • Experimental validation")
            print("      • Alternative prediction methods")
            print("      • Focus on high-confidence regions only")
        
        if breakdown.get('low_confidence_pct', 0) > 20:
            print("   ⚠️  Significant low-confidence regions detected:")
            print("      • Validate these regions experimentally")
            print("      • Consider these may be disordered or flexible")
            print("      • Use caution in these areas for drug design")
    
    # File locations
    print(f"\n📁 Output Files:")
    exp_name = summary.get('experiment_name', 'unknown')
    run_id = summary.get('run_id', 'unknown')
    
    # Try to find the experiment directory
    exp_dirs = list(Path("experiments").glob(f"*{exp_name}*"))
    if exp_dirs:
        exp_dir = exp_dirs[0]
        results_dir = exp_dir / "results"
        
        cif_files = list(results_dir.glob("*.cif"))
        json_files = list(results_dir.glob("*result.json"))
        
        if cif_files:
            print(f"   Structure (CIF): {cif_files[0]}")
        if json_files:
            print(f"   Raw Results: {json_files[0]}")
        
        print(f"   Summary: {summary_path}")
    
    print(f"\n" + "=" * 60)


def compare_quality_reports(summary_files: List[str]) -> None:
    """Compare quality metrics across multiple predictions."""
    
    print("🔍 Quality Comparison Report")
    print("=" * 60)
    
    summaries = []
    for file in summary_files:
        if Path(file).exists():
            with open(file, 'r') as f:
                summary = json.load(f)
                summary['_file'] = file
                summaries.append(summary)
        else:
            print(f"⚠️  File not found: {file}")
    
    if len(summaries) < 2:
        print("❌ Need at least 2 summary files for comparison")
        return
    
    print(f"Comparing {len(summaries)} predictions:\n")
    
    # Comparison table
    print(f"{'Experiment':<25} {'Quality':<12} {'Mean pLDDT':<12} {'High+VHigh %':<15}")
    print("-" * 70)
    
    for summary in summaries:
        name = summary.get('experiment_name', 'Unknown')[:24]
        quality = summary.get('quality_assessment', 'Unknown')
        
        conf = summary.get('confidence_scores', {})
        mean_plddt = conf.get('mean', 0)
        
        breakdown = conf.get('quality_breakdown', {})
        high_pct = breakdown.get('very_high_confidence_pct', 0) + breakdown.get('high_confidence_pct', 0)
        
        print(f"{name:<25} {quality:<12} {mean_plddt:<12.1f} {high_pct:<15.1f}")
    
    # Best/worst analysis
    if all('confidence_scores' in s and 'mean' in s['confidence_scores'] for s in summaries):
        best = max(summaries, key=lambda x: x['confidence_scores']['mean'])
        worst = min(summaries, key=lambda x: x['confidence_scores']['mean'])
        
        print(f"\n🏆 Best Quality: {best.get('experiment_name', 'Unknown')} (pLDDT: {best['confidence_scores']['mean']:.1f})")
        print(f"📉 Lowest Quality: {worst.get('experiment_name', 'Unknown')} (pLDDT: {worst['confidence_scores']['mean']:.1f})")


def main():
    """Command-line interface for quality reports."""
    parser = argparse.ArgumentParser(description="Generate protein structure quality reports")
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Single report
    single_parser = subparsers.add_parser("report", help="Generate quality report for single prediction")
    single_parser.add_argument("summary_file", help="Path to summary JSON file")
    
    # Compare reports
    compare_parser = subparsers.add_parser("compare", help="Compare multiple predictions")
    compare_parser.add_argument("summary_files", nargs="+", help="Paths to summary JSON files")
    
    # List available summaries
    list_parser = subparsers.add_parser("list", help="List available summary files")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    if args.command == "report":
        generate_quality_report(args.summary_file)
    
    elif args.command == "compare":
        compare_quality_reports(args.summary_files)
    
    elif args.command == "list":
        print("📋 Available Summary Files:")
        results_dir = Path("results")
        if results_dir.exists():
            summary_files = list(results_dir.glob("*_summary.json"))
            for i, file in enumerate(summary_files, 1):
                print(f"   {i}. {file}")
        else:
            print("   No summary files found")


if __name__ == "__main__":
    main()
